import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  ScrollView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Button from '../../../components/Button';

const CROP_OPTIONS = [
  'rice', 'millet', 'yam', 'tomatoes', 'cabbage', 'maize', 'cassava',
  'cocoa', 'plantain', 'pepper', 'onion'
];

const CropsScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCrops, setSelectedCrops] = useState<string[]>([]);
  const [filteredCrops, setFilteredCrops] = useState<string[]>(CROP_OPTIONS);

  // Filter crops based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCrops(CROP_OPTIONS);
    } else {
      const filtered = CROP_OPTIONS.filter(crop => 
        crop.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCrops(filtered);
    }
  }, [searchQuery]);

  const handleCropToggle = (crop: string) => {
    if (selectedCrops.includes(crop)) {
      const updatedCrops = selectedCrops.filter(item => item !== crop);
      setSelectedCrops(updatedCrops);
      
    } else {
      const updatedCrops = [...selectedCrops, crop];
      setSelectedCrops(updatedCrops);
      
    }
  };

  const handleContinue = () => {
    console.log('Submitting selected crops:', selectedCrops);
    router.push('/farmer/profile/farm_photos');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.title}>Farmer Registration</Text>
      <Text style={styles.subtitle}>Add crops/products for your farm</Text>
      
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Add more crops here"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <ScrollView 
        style={styles.cropsContainer} 
        contentContainerStyle={styles.cropsContent}
      >
        {filteredCrops.map((crop, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.cropItem,
              selectedCrops.includes(crop) && styles.selectedCropItem
            ]}
            onPress={() => handleCropToggle(crop)}
          >
            <Text 
              style={[
                styles.cropText,
                selectedCrops.includes(crop) && styles.selectedCropText
              ]}
            >
              {crop}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      <View style={styles.buttonContainer}>
        <Button
          title="Continue"
          onPress={handleContinue}
          disabled={selectedCrops.length === 0}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 8,
    paddingHorizontal: 20,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 25,
    marginHorizontal: 20,
    paddingHorizontal: 15,
    marginBottom: 20,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
  },
  cropsContainer: {
    flex: 1,
    paddingHorizontal: 10,
  },
  cropsContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingBottom: 20,
  },
  cropItem: {
    backgroundColor: '#F5F5F5',
    borderRadius: 25,
    paddingVertical: 10,
    paddingHorizontal: 20,
    margin: 5,
  },
  selectedCropItem: {
    backgroundColor: '#72BB53',
  },
  cropText: {
    fontSize: 16,
    color: '#333',
  },
  selectedCropText: {
    color: '#fff',
  },
  buttonContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
});

export default CropsScreen;
