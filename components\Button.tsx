import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ViewStyle, 
  TextStyle, 
  ActivityIndicator,
  Platform, 
  View
} from 'react-native';

interface ButtonProps {
  onPress: () => void;
  title: string;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

const Button: React.FC<ButtonProps> = ({
  onPress,
  title,
  variant = 'primary',
  disabled = false,
  loading = false,
  style,
  textStyle,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[
        styles.button,
        styles[variant],
        disabled && styles.disabled,
        style,
        styles.shadowEffect,
      ]}
      activeOpacity={0.7}
    >
      <View
        style={[
          styles.innerButton,
          styles[`${variant}Inner`],
          disabled && styles.disabledInner,
        ]}
      >
        {loading ? (
          <ActivityIndicator color={variant === 'primary' ? '#FFF' : '#72BB53'} />
        ) : (
          <Text style={[
            styles.text,
            styles[`${variant}Text`],
            disabled && styles.disabledText,
            textStyle
          ]}>
            {title.toUpperCase()}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#58A700',
    paddingBottom: 3,
    width: '100%',
  },
  innerButton: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 16,
    paddingVertical: 15,
    backgroundColor: '#72BB53',
  },
  primary: {
    backgroundColor: '#58A700', // darker shade for bottom layer
  },
  primaryInner: {
    backgroundColor: '#72BB53', // lighter shade for top layer
  },
  secondary: {
    backgroundColor: '#E5E5E5',
  },
  secondaryInner: {
    backgroundColor: '#F5F5F5',
  },
  disabled: {
    backgroundColor: '#CCCCCC',
  },
  disabledInner: {
    backgroundColor: '#DDDDDD',
  },
  text: {
    fontSize: 17,
    fontFamily: 'Nunito_700Bold',
    letterSpacing: 0.8,
  },
  primaryText: {
    color: '#FFFFFF',
  },
  secondaryText: {
    color: '#72BB53',
  },
  disabledText: {
    color: '#999999',
  },
  shadowEffect: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
});

export default Button;
