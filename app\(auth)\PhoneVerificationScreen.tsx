import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  SafeAreaView, 
  StatusBar,
  TextInput,
  Alert
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Entypo } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { Dropdown } from 'react-native-element-dropdown';
import Button from '../../components/Button';

type AuthStackParamList = {
  AccountType: undefined;
  PhoneVerification: undefined;
  Login: undefined;
};

type PhoneVerificationScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'PhoneVerification'>;


const countries = [
  { label: '+233', value: '233', flag: '🇬🇭' },

  
];

const formatPhoneNumber = (phone: string, countryCode: string): string => {
  // Remove all non-digit characters
  let cleaned = phone.replace(/\D/g, '');
  
  // Handle case where number starts with country code (with or without +)
  if (cleaned.startsWith(countryCode)) {
    cleaned = cleaned.substring(countryCode.length);
  }
  // Handle case where number starts with 0
  else if (cleaned.startsWith('0')) {
    cleaned = cleaned.substring(1);
  }
  
  // Return formatted number with + and country code
  return `+${countryCode}${cleaned}`;
};

const isValidGhanaNumber = (phone: string): boolean => {
  // Remove all non-digit characters
  let cleaned = phone.replace(/\D/g, '');
  
  // Handle case where number starts with country code
  if (cleaned.startsWith('233')) {
    cleaned = cleaned.substring(3);
  }
  // Handle case where number starts with 0
  else if (cleaned.startsWith('0')) {
    cleaned = cleaned.substring(1);
  }
  
  // Valid Ghana numbers have 9 digits after removing prefix
  return cleaned.length === 9;
};

const PhoneVerificationScreen: React.FC = () => {
  const navigation = useNavigation<PhoneVerificationScreenNavigationProp>();
  const { accountType } = useLocalSearchParams<{ accountType: string }>();
  const [callingCode, setCallingCode] = useState('233');
  const [phoneNumber, setPhoneNumber] = useState('');

  const handleContinue = (): void => {
    console.log('Button pressed'); // Add debug log
    
    if (!phoneNumber) {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }
  
    const formattedNumber = formatPhoneNumber(phoneNumber, callingCode);
    
    if (!isValidGhanaNumber(phoneNumber)) {
      Alert.alert('Invalid Number', 'Please enter a valid Ghana mobile number');
      return;
    }
  
    console.log('Formatted phone:', formattedNumber);
    console.log('Navigating to OTP screen with:', { phoneNumber: formattedNumber, accountType });
    
    // Try direct navigation approach
    router.push(`/(auth)/OTPScreen?phoneNumber=${encodeURIComponent(formattedNumber)}&accountType=${encodeURIComponent(accountType || 'farmer')}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Entypo name="chevron-left" size={24} color="#000" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <Text style={styles.title}>Phone Number Verification</Text>
        
        <Text style={styles.subtitle}>
          We'll use this to create your account and keep your information secure.
        </Text>
        
        <View style={styles.phoneContainer}>
          <Text style={styles.inputLabel}>Phone Number</Text>
          <View style={styles.inputRow}>
            <Dropdown
              style={styles.dropdown}
              data={countries}
              maxHeight={300}
              labelField="label"
              valueField="value"
              value={callingCode}
              onChange={item => setCallingCode(item.value)}
              renderLeftIcon={() => (
                <Text style={styles.flagText}>
                  {countries.find(c => c.value === callingCode)?.flag}
                </Text>
              )}
              renderItem={item => (
                <View style={styles.dropdownItem}>
                  <Text style={styles.flagText}>{item.flag}</Text>
                  <Text style={styles.countryText}>{item.label}</Text>
                  <Text style={styles.codeText}>+{item.value}</Text>
                </View>
              )}
            />
            <TextInput
              style={styles.phoneInput}
              placeholder="Enter phone number"
              placeholderTextColor="#A0A0A0"
              keyboardType="phone-pad"
              value={phoneNumber}
              onChangeText={(text) => {
                const cleaned = text.replace(/\D/g, '');
                setPhoneNumber(cleaned);
              }}
              maxLength={10}
            />
          </View>
        </View>
        <View style={styles.bottomContainer}>
          <Button
            title="Continue"
            onPress={handleContinue}
            disabled={!phoneNumber}/>
        </View>
      
      </View>
      <View style={styles.loginContainer}>
          <Text style={styles.loginText}>Already have an account? </Text>
          <TouchableOpacity onPress={() => navigation.navigate('Login')}>
            <Text style={styles.loginLink}>Login Now</Text>
          </TouchableOpacity>
        </View>

      
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: 10,
    marginTop: 40
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 22,
    color: '#333',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 10,
    color: '#000',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
    lineHeight: 22,
    marginBottom: 30,
  },
  phoneContainer: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 16,
    marginTop: 20,
  },
  inputLabel: {
    color: '#A0A0A0',
    fontSize: 14,
    marginBottom: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  dropdown: {
    width: 110,
    height: 40,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
    gap: 8,
  },
  flagText: {
    fontSize: 16,
  },
  countryText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  codeText: {
    fontSize: 14,
    color: '#666',
  },
  phoneInput: {
    flex: 1,
    height: 40,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    color: '#333',
  },
  bottomContainer: {
    marginTop: 40,
    width: '100%',
  },
  continueButton: {
    backgroundColor: '#72BB53',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  continueButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  continueButtonDisabled: {
    backgroundColor: '#A8A8A8',
    opacity: 0.7,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    
    marginTop: 20,
    marginBottom: 25,
  },
  loginText: {
    color: '#666',
    fontSize: 14,
  },
  loginLink: {
    color: '#CE6016',
    fontSize: 14,
    fontWeight: '600',
  },
  
});

export default PhoneVerificationScreen;
