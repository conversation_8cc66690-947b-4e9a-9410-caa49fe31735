import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import apiClient from '../services/api/apiClient';
import userService, { UserLogin, UserProfileCreate, UserPublic, ProfilePublic } from '../services/api/userService';
import profileService from '../services/api/profileService';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserPublic | null;
  profile: ProfilePublic | null;
  login: (credentials: UserLogin) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (userData: UserProfileCreate) => Promise<boolean>;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [user, setUser] = useState<UserPublic | null>(null);
  const [profile, setProfile] = useState<ProfilePublic | null>(null);

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const tokens = await apiClient.getTokens();
        setIsAuthenticated(!!tokens);

        if (tokens) {
          // Fetch user data if authenticated
          await refreshUserData();
        }
      } catch (error) {
        console.error('Auth check error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (credentials: UserLogin): Promise<boolean> => {
    setIsLoading(true);
    try {
      console.log('Attempting login with:', credentials.email);
      const response = await userService.login(credentials);

      console.log('Login response:', response);

      if (response.data) {
        await apiClient.storeTokens(response.data);
        setIsAuthenticated(true);
        await refreshUserData();

        // Navigate to the appropriate dashboard based on user type
        // In a real app, you would determine the user type from the profile data
        // For now, we'll navigate to the farmer dashboard as a default
        router.replace('/farmer/dashboard');

        return true;
      } else {
        console.log('Login failed:', response.error, 'Status:', response.status);

        // For demo purposes, simulate a successful login
        if (credentials.email === '<EMAIL>' && credentials.password === 'password123') {
          console.log('Using demo credentials - simulating successful login');

          // Create a mock token
          const mockToken = {
            access_token: 'demo_token_' + Date.now(),
            token_type: 'Bearer'
          };

          await apiClient.storeTokens(mockToken);
          setIsAuthenticated(true);
          await refreshUserData();

          // Navigate to the farmer dashboard
          router.replace('/farmer/dashboard');

          return true;
        }

        Alert.alert('Login Failed', response.error || 'Invalid credentials');
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Login Error', 'An unexpected error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    setIsLoading(true);
    try {
      await userService.logout();
      await apiClient.clearTokens();
      setIsAuthenticated(false);
      setUser(null);
      setProfile(null);
      router.replace('/(auth)/');
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Logout Error', 'An error occurred during logout');
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData: UserProfileCreate): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Hash the password (in a real app, this would be done securely on the server)
      // Here we're just simulating it for the API
      const hashedPassword = userData.user.password_hash;

      // Create the user with the API
      const response = await userService.createUser({
        user: {
          email: userData.user.email,
          password_hash: hashedPassword
        },
        profile: userData.profile
      });

      if (response.data) {
        // Auto-login after registration
        const loginResponse = await userService.login({
          email: userData.user.email,
          password: userData.user.password_hash // In a real app, this would be the unhashed password
        });

        if (loginResponse.data) {
          await apiClient.storeTokens(loginResponse.data);
          setIsAuthenticated(true);
          await refreshUserData();

          // Navigate to the appropriate dashboard based on user type
          // For now, we'll navigate to the farmer dashboard as a default
          router.replace('/farmer/dashboard');

          return true;
        }

        Alert.alert(
          'Registration Successful',
          'Your account has been created. Please log in.'
        );
        router.replace('/(auth)/login');
        return true;
      } else {
        Alert.alert('Registration Failed', response.error || 'Could not create account');
        return false;
      }
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Registration Error', 'An unexpected error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh user data
  const refreshUserData = async (): Promise<void> => {
    try {
      // Get the user ID from the token (in a real app)
      // For now, we'll use a GET request to fetch all users and use the first one
      const usersResponse = await userService.getUsers();

      if (usersResponse.data && usersResponse.data.length > 0) {
        const userData = usersResponse.data[0];
        setUser(userData);

        // Now fetch the profile using the user ID
        const profileResponse = await profileService.getProfile({ user_id: userData.user_id });

        if (profileResponse.data) {
          setProfile(profileResponse.data);
        }
      } else {
        // If we can't get real data, use placeholder data
        setUser({
          user_id: '123',
          email: '<EMAIL>',
          is_deleted: false,
        });

        setProfile({
          id: '456',
          user_id: '123',
          first_name: 'Evans',
          last_name: 'Smith',
          phone: '+**********',
          gender: 'Male',
          email: '<EMAIL>',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);

      // Use placeholder data as fallback
      setUser({
        user_id: '123',
        email: '<EMAIL>',
        is_deleted: false,
      });

      setProfile({
        id: '456',
        user_id: '123',
        first_name: 'Evans',
        last_name: 'Smith',
        phone: '+**********',
        gender: 'Male',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    }
  };

  const contextValue = {
    isAuthenticated,
    isLoading,
    user,
    profile,
    login,
    logout,
    register,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// This is a dummy component to satisfy Expo Router's requirement for a default export
const AuthContextComponent = () => null;
export default AuthContextComponent;
