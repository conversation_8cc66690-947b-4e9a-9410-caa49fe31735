import React, { createContext, useContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';

// Simple user interfaces for local use
export interface UserLogin {
  email: string;
  password: string;
}

export interface UserPublic {
  user_id: string;
  email: string;
  is_deleted?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ProfilePublic {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  phone: string;
  gender: string;
  email?: string | null;
  created_at: string;
  updated_at: string;
}

export interface UserProfileCreate {
  user: {
    email: string;
    password_hash: string;
  };
  profile: {
    first_name: string;
    last_name: string;
    phone: string;
    gender: string;
    email?: string | null;
  };
}

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: UserPublic | null;
  profile: ProfilePublic | null;
  login: (credentials: UserLogin) => Promise<boolean>;
  logout: () => Promise<void>;
  register: (userData: UserProfileCreate) => Promise<boolean>;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [user, setUser] = useState<UserPublic | null>(null);
  const [profile, setProfile] = useState<ProfilePublic | null>(null);

  // Initialize with mock data for development
  useEffect(() => {
    const initializeAuth = () => {
      // Set mock user and profile data
      setUser({
        user_id: '123',
        email: '<EMAIL>',
        is_deleted: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      setProfile({
        id: '456',
        user_id: '123',
        first_name: 'Evans',
        last_name: 'Smith',
        phone: '+**********',
        gender: 'Male',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    };

    initializeAuth();
  }, []);

  // Simplified login function for development
  const login = async (credentials: UserLogin): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Simple validation for development
      if (credentials.email && credentials.password) {
        setIsAuthenticated(true);

        // Navigate to the farmer dashboard as default
        router.replace('/farmer/dashboard');
        return true;
      } else {
        Alert.alert('Login Failed', 'Please enter valid credentials');
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Login Error', 'An unexpected error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified logout function
  const logout = async (): Promise<void> => {
    setIsLoading(true);
    try {
      setIsAuthenticated(false);
      setUser(null);
      setProfile(null);
      router.replace('/(onboarding)');
    } catch (error) {
      console.error('Logout error:', error);
      Alert.alert('Logout Error', 'An error occurred during logout');
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified register function for development
  const register = async (userData: UserProfileCreate): Promise<boolean> => {
    setIsLoading(true);
    try {
      console.log('Attempting registration with:', userData.user.email);

      // For development, just simulate successful registration
      if (userData.user.email && userData.profile.first_name && userData.profile.last_name) {
        // Update profile with the provided data
        setProfile({
          id: '456',
          user_id: '123',
          first_name: userData.profile.first_name,
          last_name: userData.profile.last_name,
          phone: userData.profile.phone,
          gender: userData.profile.gender,
          email: userData.profile.email || userData.user.email,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        setUser({
          user_id: '123',
          email: userData.user.email,
          is_deleted: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        setIsAuthenticated(true);

        // Navigate to the farmer dashboard as default
        router.replace('/farmer/dashboard');
        return true;
      } else {
        Alert.alert('Registration Failed', 'Please fill in all required fields');
        return false;
      }
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Registration Error', 'An unexpected error occurred');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified refresh user data function
  const refreshUserData = async (): Promise<void> => {
    try {
      // For development, just ensure we have mock data
      if (!user) {
        setUser({
          user_id: '123',
          email: '<EMAIL>',
          is_deleted: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      }

      if (!profile) {
        setProfile({
          id: '456',
          user_id: '123',
          first_name: 'Evans',
          last_name: 'Smith',
          phone: '+**********',
          gender: 'Male',
          email: '<EMAIL>',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  };

  const contextValue = {
    isAuthenticated,
    isLoading,
    user,
    profile,
    login,
    logout,
    register,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// This is a dummy component to satisfy Expo Router's requirement for a default export
const AuthContextComponent = () => null;
export default AuthContextComponent;
