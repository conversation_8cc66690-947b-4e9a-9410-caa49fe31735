import React from 'react';
import { View, Image, StyleSheet } from 'react-native';

interface AvatarProps {
  source: any;
  size?: number;
  showOnlineStatus?: boolean;
  isOnline?: boolean;
}

const Avatar: React.FC<AvatarProps> = ({
  source,
  size = 50,
  showOnlineStatus = false,
  isOnline = false,
}) => {
  return (
    <View style={styles.container}>
      <Image
        source={source}
        style={[
          styles.avatar,
          { width: size, height: size, borderRadius: size / 2 }
        ]}
      />
      {showOnlineStatus && isOnline && (
        <View 
          style={[
            styles.onlineIndicator,
            {
              width: size * 0.24,
              height: size * 0.24,
              borderRadius: size * 0.12,
              bottom: 0,
              right: 0,
            }
          ]} 
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  onlineIndicator: {
    position: 'absolute',
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#fff',
  },
});

export default Avatar;
