{"name": "agri-assist", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/nunito": "^0.3.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/slider": "4.5.6", "@react-navigation/stack": "^7.2.10", "expo": "53.0.22", "expo-camera": "~16.1.11", "expo-font": "~13.3.1", "expo-image-picker": "~16.1.4", "expo-location": "~18.1.6", "expo-router": "~5.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-element-dropdown": "^2.12.4", "react-native-maps": "1.20.1", "react-native-modal": "^14.0.0-rc.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "^5.3.3"}, "private": true}