import AsyncStorage from '@react-native-async-storage/async-storage';

// API base URL - using the provided backend URL
const API_BASE_URL = 'https://agricsupport.fly.dev';

// For development/testing, you might want to use a local server
// const API_BASE_URL = 'http://localhost:8000';

// Token storage keys
const ACCESS_TOKEN_KEY = 'auth_access_token';
const TOKEN_TYPE_KEY = 'auth_token_type';

// Interface for API response
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  status: number;
}

// Interface for authentication tokens
export interface AuthTokens {
  access_token: string;
  token_type: string;
}

/**
 * API Client for making HTTP requests
 */
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Store authentication tokens in AsyncStorage
   */
  async storeTokens(tokens: AuthTokens): Promise<void> {
    try {
      await AsyncStorage.setItem(ACCESS_TOKEN_KEY, tokens.access_token);
      await AsyncStorage.setItem(TOKEN_TYPE_KEY, tokens.token_type);
    } catch (error) {
      console.error('Error storing auth tokens:', error);
      throw error;
    }
  }

  /**
   * Get stored authentication tokens from AsyncStorage
   */
  async getTokens(): Promise<AuthTokens | null> {
    try {
      const accessToken = await AsyncStorage.getItem(ACCESS_TOKEN_KEY);
      const tokenType = await AsyncStorage.getItem(TOKEN_TYPE_KEY);

      if (accessToken && tokenType) {
        return {
          access_token: accessToken,
          token_type: tokenType,
        };
      }
      return null;
    } catch (error) {
      console.error('Error retrieving auth tokens:', error);
      return null;
    }
  }

  /**
   * Clear stored authentication tokens
   */
  async clearTokens(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([ACCESS_TOKEN_KEY, TOKEN_TYPE_KEY]);
    } catch (error) {
      console.error('Error clearing auth tokens:', error);
      throw error;
    }
  }

  /**
   * Get authorization header with token
   */
  async getAuthHeader(): Promise<Record<string, string> | undefined> {
    const tokens = await this.getTokens();
    if (tokens) {
      return {
        Authorization: `${tokens.token_type} ${tokens.access_token}`,
      };
    }
    return undefined;
  }

  /**
   * Make a GET request
   */
  async get<T>(endpoint: string, requireAuth: boolean = true): Promise<ApiResponse<T>> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (requireAuth) {
        const authHeader = await this.getAuthHeader();
        if (authHeader) {
          Object.assign(headers, authHeader);
        }
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'GET',
        headers,
      });

      const status = response.status;

      if (status === 401) {
        // Handle unauthorized (token expired)
        await this.clearTokens();
        return { status, error: 'Unauthorized. Please log in again.' };
      }

      if (response.ok) {
        const data = await response.json();
        return { data, status };
      } else {
        const errorData = await response.json();
        return { error: errorData.detail || 'Unknown error', status };
      }
    } catch (error) {
      console.error(`API GET error for ${endpoint}:`, error);
      return { error: 'Network error. Please check your connection.', status: 0 };
    }
  }

  /**
   * Make a POST request
   */
  async post<T>(endpoint: string, body: any, requireAuth: boolean = true): Promise<ApiResponse<T>> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (requireAuth) {
        const authHeader = await this.getAuthHeader();
        if (authHeader) {
          Object.assign(headers, authHeader);
        }
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      const status = response.status;

      if (status === 401) {
        // Handle unauthorized (token expired)
        await this.clearTokens();
        return { status, error: 'Unauthorized. Please log in again.' };
      }

      if (response.ok) {
        const data = await response.json();
        return { data, status };
      } else {
        const errorData = await response.json();
        return { error: errorData.detail || 'Unknown error', status };
      }
    } catch (error) {
      console.error(`API POST error for ${endpoint}:`, error);
      return { error: 'Network error. Please check your connection.', status: 0 };
    }
  }

  /**
   * Make a PUT request
   */
  async put<T>(endpoint: string, body: any): Promise<ApiResponse<T>> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      const authHeader = await this.getAuthHeader();
      if (authHeader) {
        Object.assign(headers, authHeader);
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(body),
      });

      const status = response.status;

      if (status === 401) {
        // Handle unauthorized (token expired)
        await this.clearTokens();
        return { status, error: 'Unauthorized. Please log in again.' };
      }

      if (response.ok) {
        const data = await response.json();
        return { data, status };
      } else {
        const errorData = await response.json();
        return { error: errorData.detail || 'Unknown error', status };
      }
    } catch (error) {
      console.error(`API PUT error for ${endpoint}:`, error);
      return { error: 'Network error. Please check your connection.', status: 0 };
    }
  }

  /**
   * Make a PATCH request
   */
  async patch<T>(endpoint: string, body: any): Promise<ApiResponse<T>> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      const authHeader = await this.getAuthHeader();
      if (authHeader) {
        Object.assign(headers, authHeader);
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PATCH',
        headers,
        body: JSON.stringify(body),
      });

      const status = response.status;

      if (status === 401) {
        // Handle unauthorized (token expired)
        await this.clearTokens();
        return { status, error: 'Unauthorized. Please log in again.' };
      }

      if (response.ok) {
        const data = await response.json();
        return { data, status };
      } else {
        const errorData = await response.json();
        return { error: errorData.detail || 'Unknown error', status };
      }
    } catch (error) {
      console.error(`API PATCH error for ${endpoint}:`, error);
      return { error: 'Network error. Please check your connection.', status: 0 };
    }
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      const authHeader = await this.getAuthHeader();
      if (authHeader) {
        Object.assign(headers, authHeader);
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers,
      });

      const status = response.status;

      if (status === 401) {
        // Handle unauthorized (token expired)
        await this.clearTokens();
        return { status, error: 'Unauthorized. Please log in again.' };
      }

      if (response.ok) {
        const data = await response.json();
        return { data, status };
      } else {
        const errorData = await response.json();
        return { error: errorData.detail || 'Unknown error', status };
      }
    } catch (error) {
      console.error(`API DELETE error for ${endpoint}:`, error);
      return { error: 'Network error. Please check your connection.', status: 0 };
    }
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;
