import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Platform, 
  StatusBar 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Avatar from './Avatar';
import { useTheme } from '../app/context/ThemeContext';

interface ChatHeaderProps {
  title: string;
  subtitle?: string;
  showBackButton?: boolean;
  showAvatar?: boolean;
  avatarSource?: any;
  isOnline?: boolean;
  rightActions?: React.ReactNode;
  onAvatarPress?: () => void;
  onBackPress?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
  title,
  subtitle,
  showBackButton = false,
  showAvatar = false,
  avatarSource,
  isOnline = false,
  rightActions,
  onAvatarPress,
  onBackPress,
}) => {
  const { colors } = useTheme();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <View style={[
      styles.header, 
      { borderBottomColor: colors.border }
    ]}>
      <View style={styles.leftSection}>
        {showBackButton && (
          <TouchableOpacity 
            style={styles.backButton}
            onPress={handleBackPress}
          >
            <Ionicons name="arrow-back" size={24} color={colors.icon} />
          </TouchableOpacity>
        )}

        {showAvatar && avatarSource && (
          <TouchableOpacity 
            style={styles.avatarContainer}
            onPress={onAvatarPress}
            disabled={!onAvatarPress}
          >
            <Avatar 
              source={avatarSource} 
              size={40} 
              showOnlineStatus={true}
              isOnline={isOnline}
            />
          </TouchableOpacity>
        )}

        <View style={styles.titleContainer}>
          <Text 
            style={[styles.title, { color: colors.text }]}
            numberOfLines={1}
          >
            {title}
          </Text>
          {subtitle && (
            <Text 
              style={[styles.subtitle, { color: colors.textSecondary }]}
              numberOfLines={1}
            >
              {subtitle}
            </Text>
          )}
        </View>
      </View>

      <View style={styles.rightSection}>
        {rightActions}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    padding: 5,
    marginRight: 5,
  },
  avatarContainer: {
    marginRight: 10,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  subtitle: {
    fontSize: 12,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default ChatHeader;
