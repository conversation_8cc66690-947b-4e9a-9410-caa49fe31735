export interface User {
  id: string;
  name: string;
  avatar: any; // Changed to any to support require() for images
  isOnline?: boolean;
  lastSeen?: string;
  type: 'farmer' | 'supplier' | 'investor';
}

export interface Message {
  id: string;
  senderId: string;
  text: string;
  timestamp: string;
  isRead: boolean;
  isPending?: boolean;
  isFailed?: boolean;
  attachments?: Attachment[];
}

export interface Attachment {
  id: string;
  type: 'image' | 'document' | 'audio' | 'location';
  url: string;
  name?: string;
  size?: number;
  thumbnail?: string;
}

export interface Conversation {
  id: string;
  participants: User[];
  lastMessage: Message;
  unreadCount: number;
  category?: string;
}
