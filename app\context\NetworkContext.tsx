import React, { createContext, useContext, useState, useEffect } from 'react';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Message } from '../types/chat';

// Key for storing pending messages in AsyncStorage
const PENDING_MESSAGES_KEY = 'pending_messages';

interface NetworkContextType {
  isConnected: boolean | null;
  isInternetReachable: boolean | null;
  pendingMessages: PendingMessage[];
  addPendingMessage: (message: PendingMessage) => Promise<void>;
  removePendingMessage: (id: string) => Promise<void>;
  processPendingMessages: () => Promise<void>;
}

export interface PendingMessage {
  id: string;
  conversationId: string;
  message: Message;
  createdAt: string;
  attempts: number;
}

const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

export const NetworkProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isInternetReachable, setIsInternetReachable] = useState<boolean | null>(null);
  const [pendingMessages, setPendingMessages] = useState<PendingMessage[]>([]);

  // Load pending messages from storage on mount
  useEffect(() => {
    const loadPendingMessages = async () => {
      try {
        const storedMessages = await AsyncStorage.getItem(PENDING_MESSAGES_KEY);
        if (storedMessages) {
          setPendingMessages(JSON.parse(storedMessages));
        }
      } catch (error) {
        console.error('Error loading pending messages:', error);
      }
    };

    loadPendingMessages();
  }, []);

  // Subscribe to network state changes
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state: NetInfoState) => {
      setIsConnected(state.isConnected);
      setIsInternetReachable(state.isInternetReachable);

      // If we're back online, try to process pending messages
      if (state.isConnected && state.isInternetReachable) {
        processPendingMessages();
      }
    });

    // Initial network check
    NetInfo.fetch().then((state: NetInfoState) => {
      setIsConnected(state.isConnected);
      setIsInternetReachable(state.isInternetReachable);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // Save pending messages to storage whenever they change
  useEffect(() => {
    const savePendingMessages = async () => {
      try {
        await AsyncStorage.setItem(PENDING_MESSAGES_KEY, JSON.stringify(pendingMessages));
      } catch (error) {
        console.error('Error saving pending messages:', error);
      }
    };

    savePendingMessages();
  }, [pendingMessages]);

  // Add a message to the pending queue
  const addPendingMessage = async (message: PendingMessage): Promise<void> => {
    setPendingMessages(prev => [...prev, message]);
  };

  // Remove a message from the pending queue
  const removePendingMessage = async (id: string): Promise<void> => {
    setPendingMessages(prev => prev.filter(msg => msg.id !== id));
  };

  // Process all pending messages
  const processPendingMessages = async (): Promise<void> => {
    if (!isConnected || !isInternetReachable || pendingMessages.length === 0) {
      return;
    }

    // Process each pending message
    const processPromises = pendingMessages.map(async (pendingMsg) => {
      try {
        // This is where you would call your API to send the message
        // For now, we'll just simulate success with a 50% chance
        const success = Math.random() > 0.5;

        if (success) {
          // If successful, remove from pending queue
          await removePendingMessage(pendingMsg.id);
          return true;
        } else {
          // If failed, increment attempt count
          setPendingMessages(prev => 
            prev.map(msg => 
              msg.id === pendingMsg.id 
                ? { ...msg, attempts: msg.attempts + 1 } 
                : msg
            )
          );
          return false;
        }
      } catch (error) {
        console.error(`Error processing pending message ${pendingMsg.id}:`, error);
        return false;
      }
    });

    await Promise.all(processPromises);
  };

  const contextValue = {
    isConnected,
    isInternetReachable,
    pendingMessages,
    addPendingMessage,
    removePendingMessage,
    processPendingMessages,
  };

  return (
    <NetworkContext.Provider value={contextValue}>
      {children}
    </NetworkContext.Provider>
  );
};

export const useNetwork = () => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};

// This is a dummy component to satisfy Expo Router's requirement for a default export
const NetworkContextComponent = () => null;
export default NetworkContextComponent;
