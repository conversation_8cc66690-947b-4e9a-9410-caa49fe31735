import { Stack } from 'expo-router';

export default function FarmerLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        animation: 'fade',
      }}
    >
      <Stack.Screen name="dashboard/index" options={{ headerShown: false }} />
      <Stack.Screen name="profile/_layout" options={{ headerShown: false }} />
      <Stack.Screen name="profile/index" options={{ headerShown: false }} />
      <Stack.Screen name="profile/crops" options={{ headerShown: false }} />
      <Stack.Screen name="profile/farm_photos" options={{ headerShown: false }} />
    </Stack>
  );
}
