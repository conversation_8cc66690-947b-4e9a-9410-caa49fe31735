import { Stack } from 'expo-router';
import { ThemeProvider } from './context/ThemeContext';
import { AuthProvider } from './context/AuthContext';
import { NetworkProvider } from './context/NetworkContext';

export default function RootLayout() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <NetworkProvider>
          <Stack
            screenOptions={{
              headerShown: false,
            }}
          >
            <Stack.Screen name="(onboarding)" />
            <Stack.Screen name="(auth)" />
            <Stack.Screen name="farmer" />
            <Stack.Screen name="supplier" />
            <Stack.Screen name="investor" />
            <Stack.Screen name="shared" />
            <Stack.Screen name="context" />
          </Stack>
        </NetworkProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}