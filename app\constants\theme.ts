export const commonColors = {
  primary: '#72BB53',
  primaryDark: '#58A700',
  secondary: '#3498db',
  success: '#2ecc71',
  warning: '#f39c12',
  danger: '#e74c3c',
  info: '#3498db',
};

export const lightTheme = {
  background: '#FFFFFF',
  surface: '#F9F9F9',
  card: '#FFFFFF',
  text: '#333333',
  textSecondary: '#666666',
  textTertiary: '#999999',
  border: '#EFEFEF',
  borderLight: '#F0F0F0',
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowIntense: 'rgba(0, 0, 0, 0.25)',
  statusBar: 'dark-content',
  tabBar: '#FFFFFF',
  tabBarBorder: '#EFEFEF',
  tabBarActive: commonColors.primary,
  tabBarInactive: '#666666',
  switchTrackActive: commonColors.primary,
  switchTrackInactive: '#E0E0E0',
  switchThumb: '#FFFFFF',
  modalBackground: 'rgba(0, 0, 0, 0.5)',
  modalContent: '#FFFFFF',
  icon: '#333333',
  iconSecondary: '#666666',
};

export const darkTheme = {
  background: '#121212',
  surface: '#1E1E1E',
  card: '#2A2A2A',
  text: '#FFFFFF',
  textSecondary: '#CCCCCC',
  textTertiary: '#999999',
  border: '#3A3A3A',
  borderLight: '#333333',
  shadow: 'rgba(0, 0, 0, 0.3)',
  shadowIntense: 'rgba(0, 0, 0, 0.5)',
  statusBar: 'light-content',
  tabBar: '#1A1A1A',
  tabBarBorder: '#333333',
  tabBarActive: commonColors.primary,
  tabBarInactive: '#AAAAAA',
  switchTrackActive: commonColors.primary,
  switchTrackInactive: '#555555',
  switchThumb: '#FFFFFF',
  modalBackground: 'rgba(0, 0, 0, 0.7)',
  modalContent: '#2A2A2A',
  icon: '#FFFFFF',
  iconSecondary: '#CCCCCC',
};

export type ThemeColors = typeof lightTheme;
