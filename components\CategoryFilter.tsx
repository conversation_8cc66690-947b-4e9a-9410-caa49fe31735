import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../app/context/ThemeContext';

export interface Category {
  id: string;
  name: string;
  icon: string;
  color?: string;
}

interface CategoryFilterProps {
  categories: Category[];
  selectedCategoryId: string;
  onSelectCategory: (categoryId: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategoryId,
  onSelectCategory,
}) => {
  const { colors, commonColors } = useTheme();
  
  return (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.container}
      contentContainerStyle={styles.content}
    >
      {categories.map(category => (
        <TouchableOpacity
          key={category.id}
          style={[
            styles.categoryButton,
            selectedCategoryId === category.id && [
              styles.selectedCategory,
              { backgroundColor: category.color || commonColors.primary }
            ]
          ]}
          onPress={() => onSelectCategory(category.id)}
        >
          <Ionicons 
            name={category.icon as any} 
            size={24} 
            color={selectedCategoryId === category.id ? '#fff' : category.color || colors.icon} 
          />
          {selectedCategoryId === category.id && (
            <View style={styles.categoryLabelContainer}>
              <Text style={styles.categoryLabel}>{category.name}</Text>
              <View style={styles.categoryDot} />
            </View>
          )}
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 15,
  },
  content: {
    paddingHorizontal: 15,
  },
  categoryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
    backgroundColor: 'transparent',
  },
  selectedCategory: {
    width: 'auto',
    paddingHorizontal: 15,
    borderRadius: 25,
    flexDirection: 'row',
  },
  categoryLabelContainer: {
    marginLeft: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryLabel: {
    color: '#fff',
    fontWeight: '600',
    marginLeft: 5,
  },
  categoryDot: {
    width: 5,
    height: 5,
    borderRadius: 2.5,
    backgroundColor: '#fff',
    marginLeft: 5,
  },
});

export default CategoryFilter;
