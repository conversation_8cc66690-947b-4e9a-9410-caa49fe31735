import { User, Message, Conversation } from '../types/chat';

// Mock Users
export const mockUsers: User[] = [
  {
    id: '1',
    name: 'Recycling Plastic PLC',
    avatar: require('../../assets/person.png'),
    isOnline: true,
    type: 'supplier'
  },
  {
    id: '2',
    name: '<PERSON>\'s Collectors',
    avatar: require('../../assets/person.png'),
    isOnline: false,
    lastSeen: '2 hours ago',
    type: 'supplier'
  },
  {
    id: '3',
    name: 'Klandenstine Earth Group',
    avatar: require('../../assets/person.png'),
    isOnline: true,
    type: 'supplier'
  },
  {
    id: '4',
    name: 'Green Earth Aggregators',
    avatar: require('../../assets/person.png'),
    isOnline: false,
    lastSeen: '1 day ago',
    type: 'supplier'
  },
  {
    id: '5',
    name: 'ReUse',
    avatar: require('../../assets/person.png'),
    isOnline: true,
    type: 'supplier'
  },
  {
    id: '6',
    name: 'ReUse 2',
    avatar: require('../../assets/person.png'),
    isOnline: false,
    lastSeen: '3 days ago',
    type: 'investor'
  },
];

// Current user (you) - will be updated dynamically
export const getCurrentUser = (profile?: any): User => ({
  id: '0',
  name: profile?.first_name ? `${profile.first_name} ${profile.last_name || ''}`.trim() : 'User',
  avatar: require('../../assets/person.png'),
  isOnline: true,
  type: 'farmer'
});

// Default current user for backwards compatibility
export const currentUser: User = {
  id: '0',
  name: 'User',
  avatar: require('../../assets/person.png'),
  isOnline: true,
  type: 'farmer'
};

// Mock Messages
export const mockMessages: Record<string, Message[]> = {
  '1': [
    {
      id: '101',
      senderId: '1',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T10:30:00',
      isRead: true,
    },
    {
      id: '102',
      senderId: '0',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T10:35:00',
      isRead: true,
    },
    {
      id: '103',
      senderId: '0',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T10:40:00',
      isRead: true,
    },
    {
      id: '104',
      senderId: '1',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T10:45:00',
      isRead: false,
    },
  ],
  '2': [
    {
      id: '201',
      senderId: '2',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T09:30:00',
      isRead: true,
    },
    {
      id: '202',
      senderId: '0',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T09:35:00',
      isRead: true,
    },
  ],
  '3': [
    {
      id: '301',
      senderId: '3',
      text: '12232qdf.jpeg',
      timestamp: '2023-12-12T08:30:00',
      isRead: true,
      attachments: [
        {
          id: 'a1',
          type: 'image',
          url: 'https://example.com/image.jpg',
          name: '12232qdf.jpeg',
          size: 1024000,
        }
      ]
    },
  ],
  '4': [
    {
      id: '401',
      senderId: '4',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T07:30:00',
      isRead: true,
    },
  ],
  '5': [
    {
      id: '501',
      senderId: '5',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T06:30:00',
      isRead: true,
    },
    {
      id: '502',
      senderId: '0',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T06:35:00',
      isRead: false,
      isPending: true,
    },
  ],
  '6': [
    {
      id: '601',
      senderId: '6',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T05:30:00',
      isRead: true,
    },
    {
      id: '602',
      senderId: '0',
      text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
      timestamp: '2023-12-12T05:35:00',
      isRead: false,
      isFailed: true,
    },
  ],
};

// Mock Conversations
export const mockConversations: Conversation[] = mockUsers.map((user, index) => {
  const messages = mockMessages[user.id] || [];
  const lastMessage = messages[messages.length - 1] || {
    id: `empty-${user.id}`,
    senderId: user.id,
    text: 'No messages yet',
    timestamp: new Date().toISOString(),
    isRead: true,
  };

  return {
    id: user.id,
    participants: [user, currentUser],
    lastMessage,
    unreadCount: messages.filter(m => m.senderId !== '0' && !m.isRead).length,
    category: index % 2 === 0 ? 'plastic' : index % 3 === 0 ? 'reuse' : 'recycling',
  };
});

// Categories for filtering
export const chatCategories = [
  { id: 'all', name: 'All Messages', icon: 'chatbubbles-outline' },
  { id: 'plastic', name: 'Plastic', icon: 'leaf-outline', color: '#72BB53' },
  { id: 'recycling', name: 'Recycling', icon: 'refresh-outline', color: '#3498db' },
  { id: 'reuse', name: 'ReUse', icon: 'repeat-outline', color: '#2c3e50' },
];

// Function to get a conversation by ID
export const getConversationById = (id: string): Conversation | undefined => {
  return mockConversations.find(conv => conv.id === id);
};

// Function to get messages for a conversation
export const getMessagesForConversation = (conversationId: string): Message[] => {
  return mockMessages[conversationId] || [];
};

// Function to get a user by ID
export const getUserById = (id: string): User | undefined => {
  if (id === '0') return currentUser;
  return mockUsers.find(user => user.id === id);
};

// Function to format timestamp
export const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    // Today: show time
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (diffInDays === 1) {
    // Yesterday
    return 'Yesterday';
  } else if (diffInDays < 7) {
    // Within a week: show day name
    return date.toLocaleDateString([], { weekday: 'short' });
  } else {
    // Older: show date
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  }
};
