import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../app/context/ThemeContext';
import Avatar from './Avatar';
import { Conversation, User } from '../app/types/chat';

interface ChatListItemProps {
  conversation: Conversation;
  currentUserId: string;
  onPress: (conversation: Conversation) => void;
  timeFormatter: (timestamp: string) => string;
}

const ChatListItem: React.FC<ChatListItemProps> = ({
  conversation,
  currentUserId,
  onPress,
  timeFormatter,
}) => {
  const { colors, commonColors } = useTheme();
  
  // Get the other participant (not the current user)
  const otherParticipant = conversation.participants.find(
    p => p.id !== currentUserId
  ) as User;
  
  const isLastMessageFromCurrentUser = conversation.lastMessage.senderId === currentUserId;
  
  return (
    <TouchableOpacity
      style={[
        styles.container,
        { borderBottomColor: colors.borderLight }
      ]}
      onPress={() => onPress(conversation)}
      activeOpacity={0.7}
    >
      <Avatar 
        source={otherParticipant.avatar} 
        size={50}
        showOnlineStatus={true}
        isOnline={otherParticipant.isOnline}
      />
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text 
            style={[
              styles.name, 
              { color: colors.text }
            ]}
            numberOfLines={1}
          >
            {otherParticipant.name}
          </Text>
          <Text 
            style={[
              styles.time, 
              { color: colors.textTertiary }
            ]}
          >
            {timeFormatter(conversation.lastMessage.timestamp)}
          </Text>
        </View>
        
        <View style={styles.messageContainer}>
          <Text 
            style={[
              styles.message,
              { color: conversation.unreadCount > 0 ? colors.text : colors.textSecondary }
            ]}
            numberOfLines={1}
          >
            {isLastMessageFromCurrentUser ? 'You: ' : ''}
            {conversation.lastMessage.text}
          </Text>
          
          {conversation.unreadCount > 0 ? (
            <View style={[styles.badge, { backgroundColor: commonColors.primary }]}>
              <Text style={styles.badgeText}>{conversation.unreadCount}</Text>
            </View>
          ) : isLastMessageFromCurrentUser && conversation.lastMessage.isRead ? (
            <Ionicons name="checkmark-done" size={16} color={commonColors.primary} />
          ) : isLastMessageFromCurrentUser ? (
            <Ionicons name="checkmark" size={16} color={colors.textTertiary} />
          ) : null}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  content: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  time: {
    fontSize: 12,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  message: {
    fontSize: 14,
    flex: 1,
    marginRight: 5,
  },
  badge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default ChatListItem;
