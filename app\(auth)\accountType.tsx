import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  SafeAreaView, 
  StatusBar,
  Platform,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons'; 
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { router } from 'expo-router';
import Button from '../../components/Button';


type AuthStackParamList = {
  AccountType: undefined;
  PhoneVerification: undefined;
  Login: undefined;

};

type AccountTypeScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'AccountType'>;

const AccountType: React.FC = () => {
  const navigation = useNavigation<AccountTypeScreenNavigationProp>();
  const [selectedType, setSelectedType] = useState(''); 

  const accountTypes = [
    {
      id: 'farmer',
      title: 'Farmer',
      description: 'Manage your farm, access funding, and grow with expert support.'
    },
    {
      id: 'investor',
      title: 'Investor',
      description: 'Discover and fund verified farms with real-time investment tracking.'
    },
    {
      id: 'supplier',
      title: 'Supplier',
      description: 'Sell agricultural products directly to farmers across the country.'
    }
  ];

  const toggleSelection = (title: string) => {
    setSelectedType(title);
  };

  const handleGetStarted = (): void => {
    console.log('Button pressed');
    
    if(!selectedType) {
      Alert.alert('Account Type Required', 'Please select an account type before proceeding.', [{ text: 'OK', style: 'default' }]);
      return;
    }
    // Pass the selectedType as a parameter
    router.push({
      pathname: '/(auth)/PhoneVerificationScreen',
      params: { accountType: selectedType }
    });
  };
  const goBack = () => {
    router.push('/(onboarding)/');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>Account Type</Text>
        
        <Text style={styles.subtitle}>
          Before you proceed, select what type of account type you would like to register as
        </Text>
        
        <View style={styles.optionsContainer}>
          {accountTypes.map((type) => (
            <TouchableOpacity
              key={type.id}
              style={styles.optionItem}
              onPress={() => toggleSelection(type.title)}
              activeOpacity={0.8}
            >
              <View style={styles.optionTextContainer}>
                <Text style={styles.optionTitle}>{type.title}</Text>
                <Text style={styles.optionDescription}>{type.description}</Text>
              </View>
              
              <View style={[
                styles.checkbox,
                selectedType === type.title && styles.checkboxSelected
              ]}>
                {selectedType === type.title && (
                  <Text style={styles.checkmark}>✓</Text>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.buttonContainer}>
          <Button
            title="Get Started"
            onPress={handleGetStarted}
            disabled={!selectedType}
            style={styles.buttonStyle}
          />
        </View>
      </View>

      

    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 15,
    color: '#000',
  },
  subtitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 30,
    lineHeight: 22,
  },
  optionsContainer: {
    marginTop: 10,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
  },
  optionTextContainer: {
    flex: 1,
    paddingRight: 10,
    paddingBottom:20
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
    color: '#000',
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#CCC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#5CB85C',
    borderColor: '#5CB85C',
  },
  checkmark: {
    color: 'white',
    fontSize: 16,
  },
  buttonContainer: {
    marginTop: 40,
    width: '100%',
  },
  buttonStyle: {
    width: '100%',
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  buttonDisabled: {
    backgroundColor: '#A8A8A8',
    opacity: 0.7,
  },
});

export default AccountType;
