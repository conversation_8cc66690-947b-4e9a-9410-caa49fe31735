import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '../context/ThemeContext';
import { testApiConnection, testLogin } from '../services/api/testConnection';
import userService from '../services/api/userService';
import profileService from '../services/api/profileService';

const ApiTestScreen: React.FC = () => {
  const { colors, statusBarStyle } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('Not tested');
  const [loginStatus, setLoginStatus] = useState<string>('Not tested');
  const [usersStatus, setUsersStatus] = useState<string>('Not tested');
  const [profilesStatus, setProfilesStatus] = useState<string>('Not tested');

  const testConnection = async () => {
    setIsLoading(true);
    setConnectionStatus('Testing...');
    
    try {
      const isConnected = await testApiConnection();
      setConnectionStatus(isConnected ? 'Connected' : 'Failed to connect');
    } catch (error) {
      console.error('Connection test error:', error);
      setConnectionStatus('Error: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  const testLoginEndpoint = async () => {
    setIsLoading(true);
    setLoginStatus('Testing...');
    
    try {
      const result = await testLogin();
      setLoginStatus(result.message);
    } catch (error) {
      console.error('Login test error:', error);
      setLoginStatus('Error: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  const testGetUsers = async () => {
    setIsLoading(true);
    setUsersStatus('Testing...');
    
    try {
      const response = await userService.getUsers();
      
      if (response.data) {
        setUsersStatus(`Success: Found ${response.data.length} users`);
      } else {
        setUsersStatus(`Failed: ${response.error || 'Unknown error'} (${response.status})`);
      }
    } catch (error) {
      console.error('Get users test error:', error);
      setUsersStatus('Error: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  const testGetProfiles = async () => {
    setIsLoading(true);
    setProfilesStatus('Testing...');
    
    try {
      const response = await profileService.getProfiles();
      
      if (response.data) {
        setProfilesStatus(`Success: Found ${response.data.length} profiles`);
      } else {
        setProfilesStatus(`Failed: ${response.error || 'Unknown error'} (${response.status})`);
      }
    } catch (error) {
      console.error('Get profiles test error:', error);
      setProfilesStatus('Error: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={statusBarStyle} />

      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={colors.icon} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>API Test</Text>
        <View style={{ width: 40 }} />
      </View>

      <ScrollView style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]}>Backend API Connection Test</Text>
        
        {isLoading && (
          <ActivityIndicator size="large" color={colors.primary} style={styles.loader} />
        )}
        
        <View style={[styles.card, { backgroundColor: colors.card }]}>
          <View style={styles.testRow}>
            <Text style={[styles.testLabel, { color: colors.text }]}>Connection:</Text>
            <Text 
              style={[
                styles.testStatus, 
                { 
                  color: connectionStatus === 'Connected' 
                    ? 'green' 
                    : connectionStatus === 'Testing...' 
                      ? colors.textSecondary 
                      : 'red' 
                }
              ]}
            >
              {connectionStatus}
            </Text>
          </View>
          
          <TouchableOpacity 
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={testConnection}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Test Connection</Text>
          </TouchableOpacity>
        </View>
        
        <View style={[styles.card, { backgroundColor: colors.card }]}>
          <View style={styles.testRow}>
            <Text style={[styles.testLabel, { color: colors.text }]}>Login:</Text>
            <Text 
              style={[
                styles.testStatus, 
                { 
                  color: loginStatus.includes('successful') 
                    ? 'green' 
                    : loginStatus === 'Testing...' 
                      ? colors.textSecondary 
                      : 'red' 
                }
              ]}
            >
              {loginStatus}
            </Text>
          </View>
          
          <TouchableOpacity 
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={testLoginEndpoint}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Test Login</Text>
          </TouchableOpacity>
        </View>
        
        <View style={[styles.card, { backgroundColor: colors.card }]}>
          <View style={styles.testRow}>
            <Text style={[styles.testLabel, { color: colors.text }]}>Get Users:</Text>
            <Text 
              style={[
                styles.testStatus, 
                { 
                  color: usersStatus.includes('Success') 
                    ? 'green' 
                    : usersStatus === 'Testing...' 
                      ? colors.textSecondary 
                      : 'red' 
                }
              ]}
            >
              {usersStatus}
            </Text>
          </View>
          
          <TouchableOpacity 
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={testGetUsers}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Test Get Users</Text>
          </TouchableOpacity>
        </View>
        
        <View style={[styles.card, { backgroundColor: colors.card }]}>
          <View style={styles.testRow}>
            <Text style={[styles.testLabel, { color: colors.text }]}>Get Profiles:</Text>
            <Text 
              style={[
                styles.testStatus, 
                { 
                  color: profilesStatus.includes('Success') 
                    ? 'green' 
                    : profilesStatus === 'Testing...' 
                      ? colors.textSecondary 
                      : 'red' 
                }
              ]}
            >
              {profilesStatus}
            </Text>
          </View>
          
          <TouchableOpacity 
            style={[styles.button, { backgroundColor: colors.primary }]}
            onPress={testGetProfiles}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Test Get Profiles</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  loader: {
    marginVertical: 20,
  },
  card: {
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  testRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  testLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  testStatus: {
    fontSize: 16,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ApiTestScreen;
