import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useOnboarding } from '../app/(onboarding)/context/OnboardingContext';

const ProgressBar: React.FC = () => {
  const { currentStep, totalSteps } = useOnboarding();
  
  return (
    <View style={styles.container}>
      <View style={styles.progressTrack}>
        <View 
          style={[
            styles.progressFill, 
            { width: `${(currentStep / totalSteps) * 100}%` }
          ]} 
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 20,
  },
  progressTrack: {
    height: 10,
    backgroundColor: '#E0E0E0',
    borderRadius: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#72BB53',
    borderRadius: 10,
  },
});

export default ProgressBar;