import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  ScrollView,
  StatusBar,
  Platform,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Button from '../../../components/Button';
import MapView, { Marker, Region } from 'react-native-maps';
import * as Location from 'expo-location';
import Modal from 'react-native-modal';

const FarmerProfileScreen: React.FC = () => {
  const mapRef = useRef<MapView>(null);
  const [formData, setFormData] = useState({
    fullName: '',
    farmSize: '',
    yearsOfExperience: '',
    location: '',
  });
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [locationName, setLocationName] = useState('');
  const [showMap, setShowMap] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [mapRegion, setMapRegion] = useState<Region>({
    latitude: 5.6037,
    longitude: -0.1870,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Permission to access location was denied');
        return;
      }
    })();
  }, []);

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    // Validate form data
    const requiredFields = ['fullName', 'farmSize', 'yearsOfExperience', 'location'] as const;
    const emptyFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);
    
    if (emptyFields.length > 0) {
      Alert.alert(
        "Missing Information",
        `Please fill in all required fields: ${emptyFields.join(', ')}`,
        [{ text: "OK" }]
      );
      return;
    }
    
    // Submit form data
    console.log('Submitting farmer profile:', formData);
    router.push('/farmer/profile/crops');
  };

  const getCurrentLocation = async () => {
    setIsLoadingLocation(true);
    try {
      const location = await Location.getCurrentPositionAsync({});
      setCurrentLocation(location);
      
      // Reverse geocode to get address
      const geocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      });
      
      if (geocode.length > 0) {
        const address = geocode[0];
        const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
        setLocationName(locationString);
        handleChange('location', locationString);
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Failed to get your current location. Please try again.');
    } finally {
      setIsLoadingLocation(false);
    }
  };

  const openMap = () => {
    setShowMap(true);
  };

  const closeMap = () => {
    setShowMap(false);
  };

  const selectLocationOnMap = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    
    // Update current location
    setCurrentLocation({
      coords: {
        latitude,
        longitude,
        altitude: null,
        accuracy: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    });
    
    // Reverse geocode to get address
    (async () => {
      try {
        const geocode = await Location.reverseGeocodeAsync({
          latitude,
          longitude
        });
        
        if (geocode.length > 0) {
          const address = geocode[0];
          const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
          setLocationName(locationString);
          handleChange('location', locationString);
        }
      } catch (error) {
        console.error('Error reverse geocoding:', error);
      }
    })();
  };



  const selectLocation = async (address: Location.LocationGeocodedAddress) => {
    const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
    setLocationName(locationString);
    handleChange('location', locationString);
    
    // Get coordinates from the address
    const geocoded = await Location.geocodeAsync(locationString);
    if (geocoded.length > 0) {
      const { latitude, longitude } = geocoded[0];
      const newRegion = {
        latitude,
        longitude,
        latitudeDelta: 0.01, // Zoom in more for better precision
        longitudeDelta: 0.01,
      };
      
      setMapRegion(newRegion);
      setCurrentLocation({
        coords: {
          latitude: geocoded[0].latitude,
          longitude: geocoded[0].longitude,
          altitude: null,
          accuracy: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null
        },
        timestamp: Date.now()
      });
      
      // Animate map to the selected location
      mapRef.current?.animateToRegion(newRegion, 1000);
    }
    
    setSearchQuery('');
  };

  const onMarkerDragEnd = async (e: any) => {
    const { latitude, longitude } = e.nativeEvent.coordinate;
    
    // Update current location
    setCurrentLocation({
      coords: {
        latitude,
        longitude,
        altitude: null,
        accuracy: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    });
    
    // Reverse geocode to get address
    try {
      const geocode = await Location.reverseGeocodeAsync({
        latitude,
        longitude
      });
      
      if (geocode.length > 0) {
        const address = geocode[0];
        const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
        setLocationName(locationString);
        handleChange('location', locationString);
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
      </View>
      <View><Text style={styles.headerTitle}>Farmer Registration</Text></View>
      <ScrollView style={styles.content}>
        
        <View style={styles.formGroup}>
          <TextInput
            style={styles.input}
            placeholder="Full Name"
            value={formData.fullName}
            onChangeText={(text) => handleChange('fullName', text)}
          />
        </View>
        
        <View style={styles.formGroup}>
          <TextInput
            style={styles.input}
            placeholder="Farm Size (in acres)"
            keyboardType="numeric"
            value={formData.farmSize}
            onChangeText={(text) => handleChange('farmSize', text)}
          />
        </View>
        
        <View style={styles.formGroup}>
          <TextInput
            style={styles.input}
            placeholder="Years of Experience"
            keyboardType="numeric"
            value={formData.yearsOfExperience}
            onChangeText={(text) => handleChange('yearsOfExperience', text)}
          />
        </View>
        
        {/* Location Section */}
        <View style={styles.formGroup}>
          <Text style={styles.sectionTitle}>Farm Location</Text>
          
          {locationName ? (
            <View style={styles.locationContainer}>
              <Text style={styles.locationLabel}>Current Set Location</Text>
              <Text style={styles.locationText}>{locationName}</Text>
            </View>
          ) : null}
          
          {/* Map View */}
          <TouchableOpacity onPress={openMap} style={styles.mapContainer}>
            <MapView
              style={styles.map}
              initialRegion={{
                latitude: currentLocation?.coords.latitude || 5.6037,
                longitude: currentLocation?.coords.longitude || -0.1870,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              }}
              scrollEnabled={false}
              zoomEnabled={false}
            >
              {currentLocation && (
                <Marker
                  coordinate={{
                    latitude: currentLocation.coords.latitude,
                    longitude: currentLocation.coords.longitude,
                  }}
                />
              )}
            </MapView>
            <View style={styles.mapOverlay}>
              <TouchableOpacity style={styles.openButton}>
                <Ionicons name="location" size={20} color="#fff" />
                <Text style={styles.openButtonText}>Open</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
          
          {/* Use Current Location Button */}
          <TouchableOpacity 
            style={styles.currentLocationButton}
            onPress={getCurrentLocation}
            disabled={isLoadingLocation}
          >
            {isLoadingLocation ? (
              <ActivityIndicator size="small" color="#72BB53" />
            ) : (
              <>
                <Ionicons name="navigate" size={20} color="#72BB53" />
                <Text style={styles.currentLocationText}>Use Current Location</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
        
        <Button
          title="Continue"
          onPress={handleSubmit}
          style={styles.submitButton}
        />
      </ScrollView>
      <Modal
        isVisible={showMap}
        style={styles.mapModal}
        onBackdropPress={closeMap}
        onBackButtonPress={closeMap}
        animationIn="slideInUp"
        animationOut="slideOutDown"
      >
        <View style={styles.mapModalContainer}>
          <View style={styles.mapModalHeader}>
            <Text style={styles.mapModalTitle}>Select Farm Location</Text>
            <TouchableOpacity onPress={closeMap}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <MapView
            ref={mapRef}
            style={styles.fullScreenMap}
            initialRegion={mapRegion}
            onPress={selectLocationOnMap}
          >
            {currentLocation && (
              <Marker
                coordinate={{
                  latitude: currentLocation.coords.latitude,
                  longitude: currentLocation.coords.longitude,
                }}
                draggable
                onDragEnd={onMarkerDragEnd}
              />
            )}
          </MapView>
          
          <View style={styles.mapModalFooter}>
            <Button
              title="Confirm Location"
              onPress={closeMap}
              style={styles.confirmLocationButton}
            />
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  headerTitle: {
    fontSize: 31,
    fontWeight: 'bold',
    marginBottom: 30,
    marginLeft: 20,
    color: '#000',
    fontFamily: 'Nunito',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  input: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  // Location styles
  locationContainer: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  locationLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 16,
    color: '#333',
  },
  mapContainer: {
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 10,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  openButton: {
    backgroundColor: '#72BB53',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 4,
  },
  openButtonText: {
    color: '#fff',
    marginLeft: 4,
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#72BB53',
    borderRadius: 8,
    padding: 12,
  },
  currentLocationText: {
    color: '#72BB53',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    marginTop: 40,
    width: '100%',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  searchInput: {
    flex: 1,
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    marginRight: 8,
  },
  searchButton: {
    backgroundColor: '#72BB53',
    borderRadius: 8,
    width: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchingText: {
    color: '#666',
    marginVertical: 10,
    textAlign: 'center',
  },
  resultsContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginBottom: 10,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  resultText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  mapModal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  mapModalContainer: {
    backgroundColor: '#fff',
    height: '90%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  mapModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
  },
  mapModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  fullScreenMap: {
    flex: 1,
  },
  mapModalFooter: {
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#EFEFEF',
  },
  confirmLocationButton: {
    backgroundColor: '#72BB53',
  }
});

export default FarmerProfileScreen;


