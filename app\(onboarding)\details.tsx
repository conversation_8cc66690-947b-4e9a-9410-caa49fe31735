import { StatusBar } from 'expo-status-bar';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { router } from 'expo-router';
import { useOnboarding } from './context/OnboardingContext';
import ProgressBar from '../../components/ProgressBar';

export default function Details() {
  const { setCurrentStep } = useOnboarding();

  const handleNavigate = () => {
    setCurrentStep(3); // Update the current step to 3
    // After onboarding, navigate to auth flow
    router.push('/(auth)/accountType');
  };

  return (
    <View style={styles.container}>
      <Image
        source={require('../../assets/splash/Group 2.png')}
        style={styles.mainImage}
      />
      <View style={styles.textContainer}>
        <Text style={styles.header}>Get Support For Your Farm</Text>
        <Text style={styles.subHeader}>
        Access funding, manage your farm, and learn smarter farming techniques.
        </Text>
        
        <TouchableOpacity onPress={handleNavigate} style={styles.buttonContainer}>
          <ProgressBar />
          <Image
            source={require('../../assets/splash/Group 1.png')}
            style={styles.buttonImage}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  mainImage: {
    marginTop: 40,
    paddingRight: 30,
    paddingLeft: 30,
    width: '100%',
    height: '60%',
    alignSelf: 'center',
    resizeMode: 'contain',
  },
  textContainer: {
    flex: 1,
    padding: 20,
  },
  header: {
    fontWeight: "400",
    fontFamily: "Nunito",
    fontSize: 25,
    paddingBottom: 10,
    textAlign: "left",
    color: '#263238'
  },
  subHeader: {
    fontSize: 17,
    fontWeight: "700",
    fontFamily: "Nunito", 
    textAlign: "left",
    color: '#263238',
    padding: 10,
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'flex-end',
    paddingRight: 20,
  },
  buttonImage: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
  },
});
