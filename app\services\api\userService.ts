import apiClient, { ApiResponse, AuthTokens } from './apiClient';

// User interfaces based on the OpenAPI spec
export interface UserLogin {
  email: string;
  password: string;
}

export interface UserCreate {
  email: string;
  password_hash: string;
}

export interface UserPublic {
  user_id: string;
  email: string;
  is_deleted?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface UserUpdate {
  email?: string | null;
}

export interface ProfileCreate {
  user_id?: string | null;
  first_name: string;
  last_name: string;
  phone: string;
  gender: string;
  email?: string | null;
}

export interface ProfilePublic {
  user_id?: string | null;
  first_name: string;
  last_name: string;
  phone: string;
  gender: string;
  id: string;
  created_at: string;
  updated_at: string;
  email: string;
}

export interface ProfileUpdate {
  first_name?: string | null;
  last_name?: string | null;
  phone?: string | null;
  gender?: string | null;
}

export interface UserProfileCreate {
  user: UserCreate;
  profile: ProfileCreate;
}

export interface UserProfilePublic {
  user: UserPublic;
  profile: ProfilePublic;
}

/**
 * User Service for handling user-related API calls
 */
class UserService {
  /**
   * Login a user
   */
  async login(credentials: UserLogin): Promise<ApiResponse<AuthTokens>> {
    return apiClient.post<AuthTokens>('/api/v1/user/login', credentials, false);
  }

  /**
   * Logout the current user
   */
  async logout(): Promise<ApiResponse<any>> {
    const response = await apiClient.post<any>('/api/v1/user/logout', {});
    if (response.status === 200) {
      await apiClient.clearTokens();
    }
    return response;
  }

  /**
   * Create a new user with profile
   */
  async createUser(userData: UserProfileCreate): Promise<ApiResponse<UserProfilePublic>> {
    return apiClient.post<UserProfilePublic>('/api/v1/user/create-user', userData, false);
  }

  /**
   * Get all users
   */
  async getUsers(): Promise<ApiResponse<UserPublic[]>> {
    return apiClient.get<UserPublic[]>('/api/v1/user/get-users');
  }

  /**
   * Get a user by ID
   */
  async getUserById(userId: string): Promise<ApiResponse<UserPublic>> {
    return apiClient.get<UserPublic>(`/api/v1/user/get-user-by-id/${userId}`);
  }

  /**
   * Update a user
   */
  async updateUser(userId: string, userData: UserUpdate): Promise<ApiResponse<UserPublic>> {
    return apiClient.put<UserPublic>(`/api/v1/user/update-user/${userId}`, userData);
  }

  /**
   * Delete a user
   */
  async deleteUser(userId: string): Promise<ApiResponse<any>> {
    return apiClient.delete<any>(`/api/v1/user/delete-user/${userId}`);
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const tokens = await apiClient.getTokens();
    return tokens !== null;
  }
}

// Export a singleton instance
export const userService = new UserService();
export default userService;
