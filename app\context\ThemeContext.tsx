import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { useColorScheme, StatusBar } from 'react-native';
import { lightTheme, darkTheme, ThemeColors, commonColors } from '../constants/theme';

export type ThemeType = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: ThemeType;
  isDarkMode: boolean;
  setTheme: (theme: ThemeType) => void;
  colors: ThemeColors;
  commonColors: typeof commonColors;
  statusBarStyle: 'dark-content' | 'light-content';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [theme, setTheme] = useState<ThemeType>('system');
  const [isDarkMode, setIsDarkMode] = useState(systemColorScheme === 'dark');

  useEffect(() => {
    // Update isDarkMode based on theme and system preference
    if (theme === 'system') {
      setIsDarkMode(systemColorScheme === 'dark');
    } else {
      setIsDarkMode(theme === 'dark');
    }
  }, [theme, systemColorScheme]);

  // Memoize the colors to prevent unnecessary re-renders
  const colors = useMemo(() => {
    return isDarkMode ? darkTheme : lightTheme;
  }, [isDarkMode]);

  // Determine status bar style based on theme
  const statusBarStyle = useMemo(() => {
    return isDarkMode ? 'light-content' as const : 'dark-content' as const;
  }, [isDarkMode]);

  // Update status bar style when theme changes
  useEffect(() => {
    StatusBar.setBarStyle(statusBarStyle);
  }, [statusBarStyle]);

  const contextValue = useMemo(() => ({
    theme,
    isDarkMode,
    setTheme,
    colors,
    commonColors,
    statusBarStyle,
  }), [theme, isDarkMode, colors, statusBarStyle]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// This is a dummy component to satisfy Expo Router's requirement for a default export
const ThemeContextComponent = () => null;
export default ThemeContextComponent;
