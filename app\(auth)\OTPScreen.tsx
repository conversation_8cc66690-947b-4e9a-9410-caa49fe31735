import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Alert,
  Platform,
  StatusBar,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import SuccessModal from '../../components/SuccessModal';
import Button from '../../components/Button';

const OTPScreen: React.FC = () => {
  const { phoneNumber, accountType } = useLocalSearchParams<{ phoneNumber: string, accountType: string }>();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [timer, setTimer] = useState(60);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const otpInputs = useRef<Array<TextInput | null>>([]);

  useEffect(() => {
    const countdown = setInterval(() => {
      setTimer((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    return () => clearInterval(countdown);
  }, []);

  const handleOtpChange = (text: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Move to next input if current input is filled
    if (text.length === 1 && index < 5) {
      otpInputs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    // Move to previous input on backspace if current input is empty
    if (e.nativeEvent.key === 'Backspace' && index > 0 && !otp[index]) {
      otpInputs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = () => {
    setLoading(true); // Show loading indicator while verifying OTP

    const otpValue = otp.join('');
    if (otpValue.length !== 6) {
      Alert.alert('Error', 'Please enter complete OTP');
      setLoading(false);
      return;
    }

    // Add your OTP verification logic here
    console.log('Verifying OTP:', otpValue);

    // In a real app, you would make an API call to verify the OTP
    // For now, we'll simulate a successful verification
    setTimeout(() => {
      // Show success modal
      setShowSuccessModal(true);
      setLoading(false);
    }, 1500);
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);

    // In a real app, you would create a user account here using the API
    // For now, we'll just navigate to the appropriate profile creation screen

    // Navigate to the appropriate profile creation screen based on account type
    if (accountType === 'Farmer') {
      router.push('/farmer/profile');
    } else if (accountType === 'Supplier') {
      router.push('/supplier/profile');
    } else if (accountType === 'Investor') {
      router.push('/investor/profile');
    } else {
      // Default fallback
      router.push('/farmer/profile');
    }
  };

  const handleResendOTP = () => {
    if (timer === 0) {
      // Add your resend OTP logic here
      setTimer(60);
      console.log('Resending OTP to:', phoneNumber);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>Enter Verification Code</Text>

        <Text style={styles.subtitle}>
          We've sent a verification code to {phoneNumber}
        </Text>

        <View style={styles.otpContainer}>
          {otp.map((digit, index) => (
            <TextInput
              key={index}
              ref={(input) => otpInputs.current[index] = input}
              style={styles.otpInput}
              value={digit}
              onChangeText={(text) => handleOtpChange(text, index)}
              onKeyPress={(e) => handleKeyPress(e, index)}
              keyboardType="number-pad"
              maxLength={1}
              selectTextOnFocus
            />
          ))}
        </View>

        <TouchableOpacity
          style={styles.resendContainer}
          onPress={handleResendOTP}
          disabled={timer > 0}
        >
          <Text style={[styles.resendText, timer > 0 && styles.resendTextDisabled]}>
            Resend Code {timer > 0 ? `(${timer}s)` : ''}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.bottomContainer}>
        <Button
          title="Verify"
          onPress={handleVerifyOTP}
          disabled={!otp.every(digit => digit)}
          loading={loading}
        />

      </View>

      <SuccessModal
        visible={showSuccessModal}
        title="Registered"
        message="You registered successfully."
        onClose={handleSuccessModalClose}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: 20,
    marginTop: Platform.OS === 'android' ? StatusBar.currentHeight : 60,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    fontSize: 24,
    textAlign: 'center',
    backgroundColor: '#F5F5F7',
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  resendText: {
    color: '#72BB53',
    fontSize: 16,
  },
  resendTextDisabled: {
    color: '#A0A0A0',
  },
  bottomContainer: {
    paddingHorizontal: 20,
    paddingBottom: 200,
    marginBotton: 100
  },
  verifyButton: {
    backgroundColor: '#72BB53',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  verifyButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  buttonDisabled: {
    backgroundColor: '#A8A8A8',
    opacity: 0.7,
  },
});

export default OTPScreen;
