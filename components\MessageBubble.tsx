import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ActivityIndicator 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../app/context/ThemeContext';
import Avatar from './Avatar';
import { Message } from '../app/types/chat';

interface MessageBubbleProps {
  message: Message;
  isCurrentUser: boolean;
  senderAvatar?: any;
  showAvatar?: boolean;
  timeFormatter: (timestamp: string) => string;
  onRetry?: (messageId: string) => void;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isCurrentUser,
  senderAvatar,
  showAvatar = true,
  timeFormatter,
  onRetry,
}) => {
  const { colors, commonColors } = useTheme();

  return (
    <View style={[
      styles.container,
      isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer,
    ]}>
      {!isCurrentUser && showAvatar && senderAvatar && (
        <Avatar 
          source={senderAvatar} 
          size={30} 
          showOnlineStatus={false}
        />
      )}
      
      <View style={[
        styles.bubble,
        isCurrentUser 
          ? [styles.currentUserBubble, { backgroundColor: commonColors.primary }]
          : [styles.otherUserBubble, { backgroundColor: colors.surface }],
      ]}>
        <Text style={[
          styles.text,
          { color: isCurrentUser ? '#fff' : colors.text }
        ]}>
          {message.text}
        </Text>
        
        <View style={styles.footer}>
          <Text style={[
            styles.time,
            { color: isCurrentUser ? 'rgba(255,255,255,0.7)' : colors.textTertiary }
          ]}>
            {timeFormatter(message.timestamp)}
          </Text>
          
          {isCurrentUser && (
            <View style={styles.status}>
              {message.isFailed ? (
                <TouchableOpacity 
                  onPress={() => onRetry && onRetry(message.id)}
                  style={styles.retryButton}
                >
                  <Ionicons name="alert-circle" size={14} color="#e74c3c" />
                  <Text style={styles.retryText}>Tap to retry</Text>
                </TouchableOpacity>
              ) : message.isPending ? (
                <ActivityIndicator size="small" color={isCurrentUser ? '#fff' : colors.textTertiary} />
              ) : message.isRead ? (
                <Ionicons name="checkmark-done" size={14} color={isCurrentUser ? '#fff' : commonColors.primary} />
              ) : (
                <Ionicons name="checkmark" size={14} color={isCurrentUser ? '#fff' : colors.textTertiary} />
              )}
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: 15,
    maxWidth: '80%',
  },
  currentUserContainer: {
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
  },
  otherUserContainer: {
    alignSelf: 'flex-start',
  },
  bubble: {
    borderRadius: 18,
    paddingHorizontal: 15,
    paddingVertical: 10,
    paddingBottom: 8,
    marginLeft: 8,
  },
  currentUserBubble: {
    borderBottomRightRadius: 5,
  },
  otherUserBubble: {
    borderBottomLeftRadius: 5,
  },
  text: {
    fontSize: 16,
    lineHeight: 22,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 5,
  },
  time: {
    fontSize: 11,
    marginRight: 5,
  },
  status: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  retryText: {
    fontSize: 11,
    color: '#e74c3c',
    marginLeft: 3,
  },
});

export default MessageBubble;
