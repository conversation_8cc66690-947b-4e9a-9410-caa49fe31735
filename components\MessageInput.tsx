import React, { useRef } from 'react';
import { 
  View, 
  TextInput, 
  StyleSheet, 
  TouchableOpacity, 
  KeyboardAvoidingView, 
  Platform 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../app/context/ThemeContext';

interface MessageInputProps {
  value: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  onAttachmentPress?: () => void;
  onVoicePress?: () => void;
  isRecording?: boolean;
  isAttachmentMenuOpen?: boolean;
  placeholder?: string;
  keyboardVerticalOffset?: number;
}

const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChangeText,
  onSend,
  onAttachmentPress,
  onVoicePress,
  isRecording = false,
  isAttachmentMenuOpen = false,
  placeholder = 'Type a message...',
  keyboardVerticalOffset = 90,
}) => {
  const { colors, commonColors } = useTheme();
  const inputRef = useRef<TextInput>(null);
  
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={keyboardVerticalOffset}
    >
      <View style={[styles.container, { 
        backgroundColor: colors.surface,
        borderTopColor: colors.border
      }]}>
        <TouchableOpacity 
          style={styles.attachButton}
          onPress={onAttachmentPress}
        >
          <Ionicons 
            name={isAttachmentMenuOpen ? "close" : "add"} 
            size={24} 
            color={colors.icon} 
          />
        </TouchableOpacity>
        
        <View style={[styles.inputContainer, { backgroundColor: colors.background }]}>
          <TextInput
            ref={inputRef}
            style={[styles.input, { color: colors.text }]}
            placeholder={placeholder}
            placeholderTextColor={colors.textTertiary}
            value={value}
            onChangeText={onChangeText}
            multiline
          />
        </View>
        
        {value.trim() ? (
          <TouchableOpacity 
            style={[styles.sendButton, { backgroundColor: commonColors.primary }]}
            onPress={onSend}
          >
            <Ionicons name="send" size={20} color="#fff" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity 
            style={[styles.voiceButton, { 
              backgroundColor: isRecording ? '#e74c3c' : colors.background 
            }]}
            onPress={onVoicePress}
          >
            <Ionicons 
              name={isRecording ? "stop" : "mic-outline"} 
              size={24} 
              color={isRecording ? '#fff' : colors.icon} 
            />
          </TouchableOpacity>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 8,
    borderTopWidth: 1,
  },
  attachButton: {
    padding: 8,
  },
  inputContainer: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 15,
    marginHorizontal: 8,
  },
  input: {
    fontSize: 16,
    maxHeight: 100,
    minHeight: 40,
    paddingTop: 10,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default MessageInput;
