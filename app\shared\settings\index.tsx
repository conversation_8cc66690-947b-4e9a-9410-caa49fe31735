import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Image,
  Platform,
  ScrollView,
  Switch,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../context/ThemeContext';

const SettingsScreen: React.FC = () => {
  const params = useLocalSearchParams();
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [showThemeModal, setShowThemeModal] = useState(false);
  const [firstName, setFirstName] = useState('Evans');
  const { theme, setTheme, colors, commonColors, statusBarStyle, isDarkMode } = useTheme();

  // Get the display text for the current theme
  const themeDisplayText = (() => {
    switch (theme) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return 'System default';
      default:
        return 'Light mode';
    }
  })();

  // Extract first name from full name if available
  useEffect(() => {
    if (params.fullName) {
      const fullName = params.fullName as string;
      const firstNameFromParams = fullName.split(' ')[0];
      if (firstNameFromParams) {
        setFirstName(firstNameFromParams);
      }
    }
  }, [params]);

  const toggleNotifications = () => {
    setNotificationsEnabled(previousState => !previousState);
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    setShowThemeModal(false);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={statusBarStyle} />

      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <View style={styles.profileSection}>
          <Image
            source={require('../../../assets/person.png')}
            style={styles.profileImage}
          />
          <Text style={[styles.greeting, { color: colors.text }]}>Hello {firstName}</Text>
        </View>
        <View style={styles.headerIcons}>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="notifications-outline" size={24} color={colors.icon} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="ellipsis-vertical" size={24} color={colors.icon} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <ScrollView style={[styles.content, { backgroundColor: colors.background }]}>
        <Text style={[styles.title, { color: colors.text }]}>Settings</Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Manage your preferences & profile information
        </Text>

        {/* Settings Cards */}
        <View style={[styles.card, {
          backgroundColor: colors.card,
          shadowColor: colors.shadow,
          borderColor: colors.border
        }]}>
          {/* Edit Profile */}
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}
            onPress={() => router.push('/shared/edit-profile')}
          >
            <View style={styles.settingLeft}>
              <Ionicons name="person-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>
                Edit profile information
              </Text>
            </View>
          </TouchableOpacity>

          {/* Notifications */}
          <View style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}>
            <View style={styles.settingLeft}>
              <Ionicons name="notifications-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>Notifications</Text>
            </View>
            <Switch
              trackColor={{
                false: colors.switchTrackInactive,
                true: colors.switchTrackActive
              }}
              thumbColor={colors.switchThumb}
              ios_backgroundColor={colors.switchTrackInactive}
              onValueChange={toggleNotifications}
              value={notificationsEnabled}
            />
          </View>

          {/* Language */}
          <TouchableOpacity style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}>
            <View style={styles.settingLeft}>
              <Ionicons name="language-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>Language</Text>
            </View>
            <View style={styles.settingRight}>
              <Text style={[styles.settingValue, { color: colors.textTertiary }]}>English</Text>
            </View>
          </TouchableOpacity>
        </View>

        <View style={[styles.card, {
          backgroundColor: colors.card,
          shadowColor: colors.shadow,
          borderColor: colors.border
        }]}>
          {/* Theme */}
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}
            onPress={() => setShowThemeModal(true)}
          >
            <View style={styles.settingLeft}>
              <Ionicons name="color-palette-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>Theme</Text>
            </View>
            <View style={styles.settingRight}>
              <Text style={[styles.settingValue, { color: colors.textTertiary }]}>
                {themeDisplayText}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        <View style={[styles.card, {
          backgroundColor: colors.card,
          shadowColor: colors.shadow,
          borderColor: colors.border
        }]}>
          {/* API Test */}
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}
            onPress={() => router.push('/shared/api-test')}
          >
            <View style={styles.settingLeft}>
              <Ionicons name="server-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>API Connection Test</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textTertiary} />
          </TouchableOpacity>
        </View>

        <View style={[styles.card, {
          backgroundColor: colors.card,
          shadowColor: colors.shadow,
          borderColor: colors.border
        }]}>
          {/* Help & Support */}
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}
            onPress={() => router.push('/shared/help-support')}
          >
            <View style={styles.settingLeft}>
              <Ionicons name="help-circle-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>Help & Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textTertiary} />
          </TouchableOpacity>

          {/* Contact us */}
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}
            onPress={() => router.push('/shared/contact-us')}
          >
            <View style={styles.settingLeft}>
              <Ionicons name="mail-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>Contact us</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textTertiary} />
          </TouchableOpacity>

          {/* Privacy policy */}
          <TouchableOpacity
            style={[styles.settingItem, { borderBottomColor: colors.borderLight }]}
            onPress={() => router.push('/shared/privacy-policy')}
          >
            <View style={styles.settingLeft}>
              <Ionicons name="shield-outline" size={24} color={colors.icon} />
              <Text style={[styles.settingText, { color: colors.text }]}>Privacy policy</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textTertiary} />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Theme Selection Modal */}
      <Modal
        visible={showThemeModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowThemeModal(false)}
      >
        <TouchableOpacity
          style={[styles.modalOverlay, { backgroundColor: colors.modalBackground }]}
          activeOpacity={1}
          onPress={() => setShowThemeModal(false)}
        >
          <View style={[styles.modalContent, {
            backgroundColor: colors.modalContent,
            shadowColor: colors.shadowIntense
          }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Select Theme</Text>

            <TouchableOpacity
              style={[
                styles.themeOption,
                { borderBottomColor: colors.borderLight },
                theme === 'light' && [styles.selectedThemeOption, { backgroundColor: colors.surface }]
              ]}
              onPress={() => handleThemeChange('light')}
            >
              <Text style={[styles.themeOptionText, { color: colors.text }]}>Light mode</Text>
              {theme === 'light' && (
                <Ionicons name="checkmark" size={20} color={commonColors.primary} />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.themeOption,
                { borderBottomColor: colors.borderLight },
                theme === 'dark' && [styles.selectedThemeOption, { backgroundColor: colors.surface }]
              ]}
              onPress={() => handleThemeChange('dark')}
            >
              <Text style={[styles.themeOptionText, { color: colors.text }]}>Dark mode</Text>
              {theme === 'dark' && (
                <Ionicons name="checkmark" size={20} color={commonColors.primary} />
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.themeOption,
                { borderBottomColor: colors.borderLight },
                theme === 'system' && [styles.selectedThemeOption, { backgroundColor: colors.surface }]
              ]}
              onPress={() => handleThemeChange('system')}
            >
              <Text style={[styles.themeOptionText, { color: colors.text }]}>System default</Text>
              {theme === 'system' && (
                <Ionicons name="checkmark" size={20} color={commonColors.primary} />
              )}
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Bottom Tab Bar */}
      <View style={[styles.tabBar, {
        backgroundColor: colors.tabBar,
        borderTopColor: colors.tabBarBorder
      }]}>
        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => router.push('/shared/(tabs)/home')}
        >
          <Ionicons name="home-outline" size={24} color={colors.tabBarInactive} />
          <Text style={[styles.tabLabel, { color: colors.tabBarInactive }]}>Home</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => router.push('/shared/(tabs)/chat')}
        >
          <Ionicons name="chatbubble-outline" size={24} color={colors.tabBarInactive} />
          <Text style={[styles.tabLabel, { color: colors.tabBarInactive }]}>Chat</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.tabItem}
          onPress={() => router.push('/shared/(tabs)/saved')}
        >
          <Ionicons name="bookmark-outline" size={24} color={colors.tabBarInactive} />
          <Text style={[styles.tabLabel, { color: colors.tabBarInactive }]}>Saved</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabItem, styles.activeTabStyle]}
          onPress={() => router.push('/shared/(tabs)/profile')}
        >
          <Ionicons name="person-outline" size={24} color={colors.tabBarActive} />
          <Text style={[styles.activeTabLabel, { color: colors.tabBarActive }]}>Profile</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  greeting: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: 15,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
  },
  card: {
    borderRadius: 12,
    marginBottom: 20,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
    borderWidth: 1,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    fontSize: 16,
    marginLeft: 15,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 16,
    marginRight: 5,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '80%',
    borderRadius: 12,
    padding: 20,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  themeOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
  },
  selectedThemeOption: {
    // Background color is applied dynamically
  },
  themeOptionText: {
    fontSize: 16,
  },
  tabBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    paddingVertical: 10,
  },
  tabItem: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  activeTabStyle: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTabLabel: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default SettingsScreen;