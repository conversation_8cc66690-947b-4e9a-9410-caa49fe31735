import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  FlatList,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useTheme } from '../../context/ThemeContext';
import {
  mockConversations,
  chatCategories,
  formatMessageTime,
  currentUser
} from '../../data/mockChatData';
import { Conversation } from '../../types/chat';

// Import reusable components
import ChatHeader from '../../../components/ChatHeader';
import ChatListItem from '../../../components/ChatListItem';
import CategoryFilter from '../../../components/CategoryFilter';

const ChatScreen: React.FC = () => {
  const { colors, statusBarStyle } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const searchInputRef = useRef<TextInput>(null);

  // Filter conversations based on search query and selected category
  const filteredConversations = mockConversations.filter(conversation => {
    const matchesSearch = searchQuery === '' ||
      conversation.participants.some(p =>
        p.name.toLowerCase().includes(searchQuery.toLowerCase())
      );

    const matchesCategory = selectedCategory === 'all' ||
      conversation.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const handleConversationPress = (conversation: Conversation) => {
    router.push({
      pathname: '/shared/chat/[id]',
      params: { id: conversation.id }
    });
  };

  // Header right actions
  const headerRightActions = (
    <View style={styles.headerIcons}>
      <TouchableOpacity style={styles.iconButton}>
        <Ionicons name="notifications-outline" size={24} color={colors.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.iconButton}>
        <Ionicons name="ellipsis-vertical" size={24} color={colors.icon} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={statusBarStyle} />

      {/* Header */}
      <ChatHeader
        title="Hello Evans"
        showAvatar={true}
        avatarSource={currentUser.avatar}
        rightActions={headerRightActions}
      />

      {/* Title and Search */}
      <View style={styles.titleContainer}>
        <Text style={[styles.title, { color: colors.text }]}>Messages</Text>
        <TouchableOpacity
          style={[styles.searchButton, { backgroundColor: colors.surface }]}
          onPress={() => searchInputRef.current?.focus()}
        >
          <Ionicons name="search" size={20} color={colors.icon} />
          <TextInput
            ref={searchInputRef}
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search"
            placeholderTextColor={colors.textTertiary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </TouchableOpacity>
      </View>

      {/* Categories */}
      <CategoryFilter
        categories={chatCategories}
        selectedCategoryId={selectedCategory}
        onSelectCategory={setSelectedCategory}
      />

      {/* Conversations List */}
      <View style={styles.listContainer}>
        <Text style={[styles.listTitle, { color: colors.text }]}>All Messages</Text>

        <FlatList
          data={filteredConversations}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <ChatListItem
              conversation={item}
              currentUserId={currentUser.id}
              onPress={handleConversationPress}
              timeFormatter={formatMessageTime}
            />
          )}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: 15,
  },
  titleContainer: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  searchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
  },
  listContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  listContent: {
    paddingBottom: 20,
  },
});

export default ChatScreen;