import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '../app/context/AuthContext';

interface DevModeDialogProps {
  visible: boolean;
  onClose: () => void;
}

const DevModeDialog: React.FC<DevModeDialogProps> = ({ visible, onClose }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [showOtpInput, setShowOtpInput] = useState(false);
  const { login } = useAuth();

  const handleStartOnboarding = () => {
    onClose();
    router.replace('/(onboarding)');
  };

  const handleContinueWithProfile = () => {
    if (!showOtpInput) {
      // Show OTP input
      if (phoneNumber.length < 10) {
        Alert.alert('Invalid Phone Number', 'Please enter a valid phone number');
        return;
      }
      setShowOtpInput(true);
    } else {
      // Verify OTP (accept any random numbers for development)
      if (otp.length < 4) {
        Alert.alert('Invalid OTP', 'Please enter a valid OTP');
        return;
      }
      
      // Simulate successful login
      login({ email: '<EMAIL>', password: 'dev123' }).then(() => {
        onClose();
      });
    }
  };

  const resetForm = () => {
    setPhoneNumber('');
    setOtp('');
    setShowOtpInput(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.dialog}>
          <View style={styles.header}>
            <Text style={styles.title}>Development Mode</Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <Text style={styles.subtitle}>
            Choose how you want to proceed:
          </Text>

          {!showOtpInput ? (
            <>
              <TouchableOpacity
                style={styles.optionButton}
                onPress={handleStartOnboarding}
              >
                <Ionicons name="refresh" size={24} color="#72BB53" />
                <View style={styles.optionContent}>
                  <Text style={styles.optionTitle}>Start Complete Onboarding</Text>
                  <Text style={styles.optionDescription}>
                    Go through the full onboarding process from the beginning
                  </Text>
                </View>
              </TouchableOpacity>

              <View style={styles.divider} />

              <Text style={styles.sectionTitle}>Continue with Existing Profile</Text>
              <Text style={styles.sectionDescription}>
                Enter any phone number to simulate login:
              </Text>

              <TextInput
                style={styles.input}
                placeholder="Enter phone number"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="phone-pad"
                maxLength={15}
              />

              <TouchableOpacity
                style={[styles.continueButton, !phoneNumber && styles.disabledButton]}
                onPress={handleContinueWithProfile}
                disabled={!phoneNumber}
              >
                <Text style={[styles.continueButtonText, !phoneNumber && styles.disabledButtonText]}>
                  Send OTP
                </Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              <Text style={styles.sectionTitle}>Enter OTP</Text>
              <Text style={styles.sectionDescription}>
                Enter any 4+ digit code to continue (development mode):
              </Text>

              <TextInput
                style={styles.input}
                placeholder="Enter OTP"
                value={otp}
                onChangeText={setOtp}
                keyboardType="number-pad"
                maxLength={6}
              />

              <View style={styles.buttonRow}>
                <TouchableOpacity
                  style={styles.backButton}
                  onPress={() => setShowOtpInput(false)}
                >
                  <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.continueButton, !otp && styles.disabledButton]}
                  onPress={handleContinueWithProfile}
                  disabled={!otp}
                >
                  <Text style={[styles.continueButtonText, !otp && styles.disabledButtonText]}>
                    Continue
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dialog: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 15,
  },
  optionContent: {
    marginLeft: 15,
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 15,
  },
  continueButton: {
    backgroundColor: '#72BB53',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  disabledButtonText: {
    color: '#999',
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 10,
  },
  backButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  backButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default DevModeDialog;
