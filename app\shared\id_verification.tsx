import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import Button from '../../components/Button';
import { Camera, CameraType } from 'expo-camera';

const IDVerificationScreen = () => {
  const params = useLocalSearchParams();
  const accountType = params.accountType as string;
  
  const [frontPhoto, setFrontPhoto] = useState<string | null>(null);
  const [backPhoto, setBackPhoto] = useState<string | null>(null);
  const [currentSide, setCurrentSide] = useState<'front' | 'back'>('front');
  const [isVerifying, setIsVerifying] = useState(false);
  const cameraRef = useRef<Camera | null>(null);
  const [permission, requestPermission] = Camera.useCameraPermissions();
  const hasPermission = permission?.granted ?? null;

  useEffect(() => {
    if (!permission?.granted) {
      requestPermission();
    }
  }, [permission]);

  const takePhoto = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePictureAsync({ quality: 0.8 });
        if (currentSide === 'front') {
          setFrontPhoto(photo.uri);
        } else {
          setBackPhoto(photo.uri);
        }
      } catch (error) {
        console.error('Error taking picture:', error);
        Alert.alert('Error', 'Failed to capture image. Please try again.');
      }
    }
  };

  const retakePhoto = () => {
    if (currentSide === 'front') {
      setFrontPhoto(null);
    } else {
      setBackPhoto(null);
    }
  };

  const switchSide = () => {
    setCurrentSide(currentSide === 'front' ? 'back' : 'front');
  };

  const handleVerification = () => {
    if (!frontPhoto || !backPhoto) {
      Alert.alert('Missing Photos', 'Please capture both sides of your Ghana card');
      return;
    }

    setIsVerifying(true);
    
    // Simulate verification process
    setTimeout(() => {
      setIsVerifying(false);
      
      // Navigate to the appropriate next screen based on account type
      if (accountType === 'farmer') {
        router.push('/farmer/profile/crops');
      } else if (accountType === 'supplier') {
        router.push('/supplier/dashboard');
      } else if (accountType === 'investor') {
        router.push('/investor/dashboard');
      } else {
        // Default fallback
        router.push('/');
      }
    }, 2000);
  };

  const getCurrentPhoto = () => {
    return currentSide === 'front' ? frontPhoto : backPhoto;
  };

  const renderCameraView = () => {
    const currentPhoto = getCurrentPhoto();
    
    if (currentPhoto) {
      return (
        <View style={styles.photoContainer}>
          <Image source={{ uri: currentPhoto }} style={styles.cameraPreview} />
          <View style={styles.photoActions}>
            <TouchableOpacity 
              style={styles.photoActionButton}
              onPress={retakePhoto}
            >
              <Ionicons name="refresh" size={20} color="#fff" />
              <Text style={styles.photoActionText}>Retake</Text>
            </TouchableOpacity>
            
            {!isVerifying && (currentSide === 'front' && !backPhoto) && (
              <TouchableOpacity 
                style={styles.photoActionButton}
                onPress={switchSide}
              >
                <Ionicons name="arrow-forward" size={20} color="#fff" />
                <Text style={styles.photoActionText}>Next Side</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      );
    }
    
    if (hasPermission) {
      return (
        <View style={styles.cameraContainer}>
          <Camera
            ref={cameraRef}
            style={styles.cameraPreview}
            type={CameraType.back}
          />
          
          <View style={styles.cameraOverlay}>
            <View style={styles.cardOutline} />
          </View>
          
          <TouchableOpacity 
            style={styles.captureButton}
            onPress={takePhoto}
          >
            <Ionicons name="camera" size={30} color="#fff" />
          </TouchableOpacity>
        </View>
      );
    }
    
    return (
      <View style={styles.cameraPlaceholder}>
        <Text style={styles.placeholderText}>Camera permission not granted</Text>
        <TouchableOpacity 
          style={styles.permissionButton}
          onPress={requestPermission}
        >
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
        
        <View style={styles.progressContainer}>
          <View style={[styles.progressStep, { backgroundColor: frontPhoto ? '#72BB53' : '#E0E0E0' }]}>
            <Text style={[styles.progressText, { color: frontPhoto ? '#fff' : '#666' }]}>1</Text>
          </View>
          <View style={styles.progressLine} />
          <View style={[styles.progressStep, { backgroundColor: backPhoto ? '#72BB53' : '#E0E0E0' }]}>
            <Text style={[styles.progressText, { color: backPhoto ? '#fff' : '#666' }]}>2</Text>
          </View>
        </View>
      </View>
      
      <Text style={styles.title}>ID Verification</Text>
      <Text style={styles.subtitle}>
        Take a photo of your Ghana card ({currentSide === 'front' ? 'front' : 'back'} side)
      </Text>
      
      {renderCameraView()}
      
      <View style={styles.buttonContainer}>
        {isVerifying ? (
          <View style={styles.verifyingContainer}>
            <ActivityIndicator size="large" color="#72BB53" />
            <Text style={styles.verifyingText}>Verifying your ID...</Text>
          </View>
        ) : (
          <Button
            title="Complete Verification"
            onPress={handleVerification}
            disabled={!frontPhoto || !backPhoto}
          />
        )}
      </View>
    </SafeAreaView>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 40,
  },
  progressStep: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressLine: {
    width: 50,
    height: 2,
    backgroundColor: '#E0E0E0',
  },
  progressText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 8,
    paddingHorizontal: 20,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    paddingHorizontal: 20,
  },
  cameraContainer: {
    flex: 1,
    marginHorizontal: 20,
    position: 'relative',
    marginBottom: 20,
    borderRadius: 8,
    overflow: 'hidden',
  },
  cameraPreview: {
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },
  cameraOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardOutline: {
    width: '80%',
    height: '50%',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 8,
  },
  cameraPlaceholder: {
    flex: 1,
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
  },
  placeholderText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  permissionButton: {
    backgroundColor: '#72BB53',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  captureButton: {
    position: 'absolute',
    bottom: 20,
    alignSelf: 'center',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#CE6016',
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoContainer: {
    flex: 1,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  photoActions: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
  },
  photoActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 5,
  },
  photoActionText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  buttonContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  verifyingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
    paddingVertical: 10,
  },
  verifyingText: {
    fontSize: 16,
    color: '#666',
  },
});

export default IDVerificationScreen;
