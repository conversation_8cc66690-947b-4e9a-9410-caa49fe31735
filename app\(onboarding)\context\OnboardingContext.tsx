import React, { createContext, useContext, useState } from 'react';

type OnboardingContextType = {
  totalSteps: number;
  currentStep: number;
  setCurrentStep: (step: number) => void;
};

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 2;

  return (
    <OnboardingContext.Provider value={{ totalSteps, currentStep, setCurrentStep }}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (!context) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};

// This is a dummy component to satisfy Expo Router's requirement for a default export
const OnboardingContextComponent = () => null;
export default OnboardingContextComponent;
