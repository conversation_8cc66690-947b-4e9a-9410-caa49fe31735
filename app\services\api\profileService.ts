import apiClient, { ApiResponse } from './apiClient';
import { ProfilePublic, ProfileUpdate } from './userService';

/**
 * Profile Service for handling profile-related API calls
 */
class ProfileService {
  /**
   * Get all profiles
   */
  async getProfiles(): Promise<ApiResponse<ProfilePublic[]>> {
    return apiClient.get<ProfilePublic[]>('/api/v1/profile');
  }

  /**
   * Get a profile by profile ID or user ID
   */
  async getProfile(params: { profile_id?: string; user_id?: string }): Promise<ApiResponse<ProfilePublic>> {
    const queryParams = new URLSearchParams();
    
    if (params.profile_id) {
      queryParams.append('profile_id', params.profile_id);
    }
    
    if (params.user_id) {
      queryParams.append('user_id', params.user_id);
    }
    
    const queryString = queryParams.toString();
    const endpoint = `/api/v1/profile/search${queryString ? `?${queryString}` : ''}`;
    
    return apiClient.get<ProfilePublic>(endpoint);
  }

  /**
   * Update a profile
   */
  async updateProfile(profileId: string, profileData: ProfileUpdate): Promise<ApiResponse<ProfilePublic>> {
    return apiClient.patch<ProfilePublic>(`/api/v1/profile/${profileId}`, profileData);
  }

  /**
   * Delete a profile
   */
  async deleteProfile(profileId: string): Promise<ApiResponse<ProfilePublic>> {
    return apiClient.delete<ProfilePublic>(`/api/v1/profile/${profileId}`);
  }

  /**
   * Get the current user's profile
   * This is a convenience method that first gets the current user's ID from the token
   * and then fetches their profile
   */
  async getCurrentUserProfile(): Promise<ApiResponse<ProfilePublic>> {
    // This is a placeholder implementation
    // In a real app, you would decode the JWT token to get the user ID
    // or make a request to a /me endpoint if available
    
    // For now, we'll assume we have the user ID stored somewhere
    const userId = await this.getCurrentUserId();
    
    if (!userId) {
      return { 
        status: 401, 
        error: 'Not authenticated or user ID not available' 
      };
    }
    
    return this.getProfile({ user_id: userId });
  }

  /**
   * Helper method to get the current user ID
   * In a real app, this would come from the JWT token or a user context
   */
  private async getCurrentUserId(): Promise<string | null> {
    // This is a placeholder
    // In a real app, you would decode the JWT token or use a stored user ID
    
    // For now, we'll return null to indicate we don't have a user ID
    return null;
  }
}

// Export a singleton instance
export const profileService = new ProfileService();
export default profileService;
