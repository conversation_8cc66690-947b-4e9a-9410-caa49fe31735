import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  FlatList,
  Keyboard,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../context/ThemeContext';
import { useNetwork } from '../../context/NetworkContext';
import {
  getConversationById,
  getMessagesForConversation,
  getUserById,
  formatMessageTime,
  currentUser,
} from '../../data/mockChatData';
import { Message } from '../../types/chat';

// Import reusable components
import ChatHeader from '../../../components/ChatHeader';
import MessageBubble from '../../../components/MessageBubble';
import MessageInput from '../../../components/MessageInput';

const ChatDetailScreen: React.FC = () => {
  const { colors, commonColors, statusBarStyle } = useTheme();
  const { isConnected, isInternetReachable, addPendingMessage } = useNetwork();
  const params = useLocalSearchParams();
  const { id } = params;
  const conversationId = typeof id === 'string' ? id : '';

  const conversation = getConversationById(conversationId);
  const [messages, setMessages] = useState<Message[]>(
    getMessagesForConversation(conversationId)
  );
  const [newMessage, setNewMessage] = useState('');
  const [isAttachmentMenuOpen, setIsAttachmentMenuOpen] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isOffline, setIsOffline] = useState(false);

  const flatListRef = useRef<FlatList>(null);

  // Get the other participant (not the current user)
  const otherParticipant = conversation?.participants.find(p => p.id !== currentUser.id);

  useEffect(() => {
    // Simulate marking messages as read when opening the chat
    const updatedMessages = messages.map(msg => ({
      ...msg,
      isRead: true,
    }));
    setMessages(updatedMessages);

    // Scroll to bottom on initial load
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: false });
    }, 100);
  }, []);

  // Update offline status based on network context
  useEffect(() => {
    setIsOffline(!(isConnected && isInternetReachable));
  }, [isConnected, isInternetReachable]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const messageText = newMessage.trim();
    setNewMessage('');

    // Create a new message
    const newMsg: Message = {
      id: `new-${Date.now()}`,
      senderId: currentUser.id,
      text: messageText,
      timestamp: new Date().toISOString(),
      isRead: false,
      isPending: isOffline,
    };

    // Add to messages
    setMessages(prev => [...prev, newMsg]);

    // If offline, add to pending messages queue in NetworkContext
    if (isOffline) {
      addPendingMessage({
        id: newMsg.id,
        conversationId,
        message: newMsg,
        createdAt: new Date().toISOString(),
        attempts: 0
      });
    }

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    // Simulate reply after 2 seconds
    if (!isOffline && Math.random() > 0.3) {
      setTimeout(() => {
        const replyMsg: Message = {
          id: `reply-${Date.now()}`,
          senderId: otherParticipant?.id || '',
          text: 'Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem ipsum has been the industry\'s standard dummy',
          timestamp: new Date().toISOString(),
          isRead: true,
        };

        setMessages(prev => [...prev, replyMsg]);

        // Scroll to bottom
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }, 2000);
    }
  };

  const handleAttachmentPress = () => {
    setIsAttachmentMenuOpen(!isAttachmentMenuOpen);
    Keyboard.dismiss();
  };

  const handleVoicePress = () => {
    setIsRecording(!isRecording);
    Keyboard.dismiss();
  };

  const handleRetryFailedMessage = (messageId: string) => {
    // Simulate message retry
    const updatedMessages = messages.map(msg =>
      msg.id === messageId
        ? { ...msg, isFailed: false, isPending: true }
        : msg
    );
    setMessages(updatedMessages);

    // Simulate success after 1 second
    setTimeout(() => {
      const finalMessages = messages.map(msg =>
        msg.id === messageId
          ? { ...msg, isFailed: false, isPending: false, isRead: true }
          : msg
      );
      setMessages(finalMessages);
    }, 1000);
  };



  const renderDateSeparator = (date: string) => (
    <View style={styles.dateSeparator}>
      <View style={[styles.dateLine, { backgroundColor: colors.borderLight }]} />
      <Text style={[styles.dateText, { color: colors.textSecondary, backgroundColor: colors.background }]}>
        {date}
      </Text>
      <View style={[styles.dateLine, { backgroundColor: colors.borderLight }]} />
    </View>
  );

  if (!conversation || !otherParticipant) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <StatusBar barStyle={statusBarStyle} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={commonColors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading conversation...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Header right actions
  const headerRightActions = (
    <View style={styles.headerActions}>
      <TouchableOpacity style={styles.headerButton}>
        <Ionicons name="call-outline" size={22} color={colors.icon} />
      </TouchableOpacity>
      <TouchableOpacity style={styles.headerButton}>
        <Ionicons name="ellipsis-vertical" size={22} color={colors.icon} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar barStyle={statusBarStyle} />

      {/* Header */}
      <ChatHeader
        title={otherParticipant.name}
        subtitle={otherParticipant.isOnline ? 'Online' : otherParticipant.lastSeen || 'Offline'}
        showBackButton={true}
        showAvatar={true}
        avatarSource={otherParticipant.avatar}
        isOnline={otherParticipant.isOnline}
        rightActions={headerRightActions}
      />

      {/* Offline Indicator */}
      {isOffline && (
        <View style={[styles.offlineIndicator, { backgroundColor: '#f39c12' }]}>
          <Ionicons name="cloud-offline-outline" size={16} color="#fff" />
          <Text style={styles.offlineText}>
            You're offline. Messages will be sent when you're back online.
          </Text>
        </View>
      )}

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={item => item.id}
        renderItem={({ item }) => {
          const isCurrentUser = item.senderId === currentUser.id;
          const sender = getUserById(item.senderId);

          return (
            <MessageBubble
              message={item}
              isCurrentUser={isCurrentUser}
              senderAvatar={sender?.avatar}
              timeFormatter={formatMessageTime}
              onRetry={handleRetryFailedMessage}
            />
          );
        }}
        contentContainerStyle={styles.messagesList}
        ListHeaderComponent={() => renderDateSeparator('Yesterday')}
        showsVerticalScrollIndicator={false}
      />

      {/* Attachment Menu */}
      {isAttachmentMenuOpen && (
        <View style={[styles.attachmentMenu, { backgroundColor: colors.surface, borderTopColor: colors.border }]}>
          <TouchableOpacity style={styles.attachmentOption}>
            <View style={[styles.attachmentIcon, { backgroundColor: '#4CAF50' }]}>
              <Ionicons name="camera-outline" size={24} color="#fff" />
            </View>
            <Text style={[styles.attachmentText, { color: colors.text }]}>Camera</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.attachmentOption}>
            <View style={[styles.attachmentIcon, { backgroundColor: '#3498db' }]}>
              <Ionicons name="image-outline" size={24} color="#fff" />
            </View>
            <Text style={[styles.attachmentText, { color: colors.text }]}>Gallery</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.attachmentOption}>
            <View style={[styles.attachmentIcon, { backgroundColor: '#9b59b6' }]}>
              <Ionicons name="document-outline" size={24} color="#fff" />
            </View>
            <Text style={[styles.attachmentText, { color: colors.text }]}>Document</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.attachmentOption}>
            <View style={[styles.attachmentIcon, { backgroundColor: '#e74c3c' }]}>
              <Ionicons name="location-outline" size={24} color="#fff" />
            </View>
            <Text style={[styles.attachmentText, { color: colors.text }]}>Location</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Message Input */}
      <MessageInput
        value={newMessage}
        onChangeText={setNewMessage}
        onSend={handleSendMessage}
        onAttachmentPress={handleAttachmentPress}
        onVoicePress={handleVoicePress}
        isRecording={isRecording}
        isAttachmentMenuOpen={isAttachmentMenuOpen}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 5,
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingHorizontal: 15,
  },
  offlineText: {
    color: '#fff',
    fontSize: 12,
    marginLeft: 5,
  },
  messagesList: {
    padding: 15,
    paddingBottom: 20,
  },
  dateSeparator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 15,
  },
  dateLine: {
    flex: 1,
    height: 1,
  },
  dateText: {
    fontSize: 12,
    marginHorizontal: 10,
    paddingHorizontal: 5,
  },
  attachmentMenu: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 15,
    borderTopWidth: 1,
  },
  attachmentOption: {
    alignItems: 'center',
  },
  attachmentIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
  attachmentText: {
    fontSize: 12,
  },
});

export default ChatDetailScreen;
