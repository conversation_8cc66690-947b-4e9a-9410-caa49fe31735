import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  ScrollView,
  StatusBar,
  Platform,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Button from '../../../components/Button';
import SuccessModal from '../../../components/SuccessModal';
import * as Location from 'expo-location';
import MapView, { Marker, Region } from 'react-native-maps';
import Modal from 'react-native-modal'

const INDUSTRY_TYPES = [
  'Agriculture',
  'Food Processing',
  'Farm Equipment',
  'Seeds & Fertilizers',
  'Pesticides & Herbicides',
  'Irrigation Systems',
  'Packaging Materials',
  'Transportation & Logistics',
  'Other'
];

const PRODUCT_CATEGORIES = [
  'rice', 'millet', 'yam', 'tomatoes', 'cabbage', 'maze', 'cassava',
  'cocoa', 'plantain', 'pepper', 'onions'
];

const SupplierProfileScreen: React.FC = () => {
  const mapRef = useRef<MapView>(null);
  const [formData, setFormData] = useState({
    companyName: '',
    businessRegistration: '',
    contactPerson: '',
    industryType: '',
    location: '',
  });
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showIndustryDropdown, setShowIndustryDropdown] = useState(false);
  const [showProductsDropdown, setShowProductsDropdown] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [locationName, setLocationName] = useState('');
  const [showMap, setShowMap] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [mapRegion, setMapRegion] = useState<Region>({
    latitude: 5.6037,
    longitude: -0.1870,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [isSearching, setIsSearching] = useState(false);

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleProductToggle = (product: string) => {
    if (selectedProducts.includes(product)) {
      setSelectedProducts(selectedProducts.filter(item => item !== product));
    } else {
      setSelectedProducts([...selectedProducts, product]);
    }
  };

  const handleSubmit = () => {
    // Validate form data
    if (!isFormValid()) {
      return;
    }

    console.log('Submitting supplier profile:', {
      ...formData,
      productCategories: selectedProducts
    });

    // Show success modal
    setShowSuccessModal(true);
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    // Navigate to settings
    router.push({
      pathname: '/shared/settings',
      params: { fullName: formData.contactPerson }
    });
  };

  const isFormValid = () => {
    return (
      formData.companyName.trim() !== '' &&
      formData.businessRegistration.trim() !== '' &&
      formData.contactPerson.trim() !== '' &&
      formData.industryType.trim() !== '' &&
      formData.location.trim() !== '' &&
      selectedProducts.length > 0
    );
  };

  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Permission to access location was denied');
        return;
      }
    })();
  }, []);

  const getCurrentLocation = async () => {
    setIsLoadingLocation(true);
    try {
      const location = await Location.getCurrentPositionAsync({});
      setCurrentLocation(location);

      // Reverse geocode to get address
      const geocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      });

      if (geocode.length > 0) {
        const address = geocode[0];
        const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
        setLocationName(locationString);
        handleChange('location', locationString);
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Failed to get your current location. Please try again.');
    } finally {
      setIsLoadingLocation(false);
    }
  };

  const openMap = () => {
    setShowMap(true);
  };

  const closeMap = () => {
    setShowMap(false);
  };


  const selectLocationOnMap = (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;

    // Update current location
    setCurrentLocation({
      coords: {
        latitude,
        longitude,
        altitude: null,
        accuracy: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    });

    // Reverse geocode to get address
    (async () => {
      try {
        const geocode = await Location.reverseGeocodeAsync({
          latitude,
          longitude
        });

        if (geocode.length > 0) {
          const address = geocode[0];
          const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
          setLocationName(locationString);
          handleChange('location', locationString);
        }
      } catch (error) {
        console.error('Error reverse geocoding:', error);
      }
    })();
  };

    const selectLocation = async (address: Location.LocationGeocodedAddress) => {
      const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
      setLocationName(locationString);
      handleChange('location', locationString);

      // Get coordinates from the address
      const geocoded = await Location.geocodeAsync(locationString);
      if (geocoded.length > 0) {
        const { latitude, longitude } = geocoded[0];
        const newRegion = {
          latitude,
          longitude,
          latitudeDelta: 0.01, // Zoom in more for better precision
          longitudeDelta: 0.01,
        };

        setMapRegion(newRegion);
        setCurrentLocation({
          coords: {
            latitude: geocoded[0].latitude,
            longitude: geocoded[0].longitude,
            altitude: null,
            accuracy: null,
            altitudeAccuracy: null,
            heading: null,
            speed: null
          },
          timestamp: Date.now()
        });

        // Animate map to the selected location
        mapRef.current?.animateToRegion(newRegion, 1000);
      }

      setSearchQuery('');
    };

    const onMarkerDragEnd = async (e: any) => {
      const { latitude, longitude } = e.nativeEvent.coordinate;

      // Update current location
      setCurrentLocation({
        coords: {
          latitude,
          longitude,
          altitude: null,
          accuracy: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null
        },
        timestamp: Date.now()
      });

      // Reverse geocode to get address
      try {
        const geocode = await Location.reverseGeocodeAsync({
          latitude,
          longitude
        });

        if (geocode.length > 0) {
          const address = geocode[0];
          const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
          setLocationName(locationString);
          handleChange('location', locationString);
        }
      } catch (error) {
        console.error('Error reverse geocoding:', error);
      }
    };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.title}>Supplier Registration</Text>

        <View style={styles.formGroup}>
          <TextInput
            style={styles.input}
            placeholder="Company name"
            value={formData.companyName}
            onChangeText={(text) => handleChange('companyName', text)}
          />
        </View>

        <View style={styles.formGroup}>
          <TextInput
            style={styles.input}
            placeholder="Business Registration Number"
            value={formData.businessRegistration}
            onChangeText={(text) => handleChange('businessRegistration', text)}
          />
        </View>

        <View style={styles.formGroup}>
          <TextInput
            style={styles.input}
            placeholder="Contact person details"
            value={formData.contactPerson}
            onChangeText={(text) => handleChange('contactPerson', text)}
          />
        </View>

        <View style={styles.formGroup}>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() => setShowIndustryDropdown(!showIndustryDropdown)}
          >
            <Text style={formData.industryType ? styles.inputText : styles.placeholderText}>
              {formData.industryType || "Industry type selection"}
            </Text>
            <Ionicons name="chevron-down" size={20} color="#666" />
          </TouchableOpacity>

          {showIndustryDropdown && (
            <View style={styles.dropdownContainer}>
              <ScrollView style={styles.dropdownScroll} nestedScrollEnabled={true}>
                {INDUSTRY_TYPES.map((type, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.dropdownItem}
                    onPress={() => {
                      handleChange('industryType', type);
                      setShowIndustryDropdown(false);
                    }}
                  >
                    <Text style={styles.dropdownItemText}>{type}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>

        {/* Product Categories Dropdown */}
        <View style={styles.formGroup}>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() => setShowProductsDropdown(!showProductsDropdown)}
          >
            <Text style={selectedProducts.length > 0 ? styles.inputText : styles.placeholderText}>
              {selectedProducts.length > 0
                ? `${selectedProducts.length} product${selectedProducts.length > 1 ? 's' : ''} selected`
                : "Select product categories"}
            </Text>
            <Ionicons name="chevron-down" size={20} color="#666" />
          </TouchableOpacity>

          {showProductsDropdown && (
            <View style={styles.dropdownContainer}>
              <ScrollView style={styles.dropdownScroll} nestedScrollEnabled={true}>
                {PRODUCT_CATEGORIES.map((product, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.dropdownItem,
                      selectedProducts.includes(product) && styles.selectedDropdownItem
                    ]}
                    onPress={() => handleProductToggle(product)}
                  >
                    <Text style={[
                      styles.dropdownItemText,
                      selectedProducts.includes(product) && styles.selectedDropdownItemText
                    ]}>
                      {product}
                    </Text>
                    {selectedProducts.includes(product) && (
                      <Ionicons name="checkmark" size={18} color="#72BB53" />
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}

          {selectedProducts.length > 0 && (
            <View style={styles.selectedProductsContainer}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {selectedProducts.map((product, index) => (
                  <View key={index} style={styles.selectedProductChip}>
                    <Text style={styles.selectedProductChipText}>{product}</Text>
                    <TouchableOpacity onPress={() => handleProductToggle(product)}>
                      <Ionicons name="close-circle" size={18} color="#666" />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}
        </View>
          {/* Location Section */}
          <View style={styles.formGroup}>
          <Text style={styles.sectionTitle}>Business Location</Text>

          {locationName ? (
            <View style={styles.locationContainer}>
              <Text style={styles.locationLabel}>Current Set Location</Text>
              <Text style={styles.locationText}>{locationName}</Text>
            </View>
          ) : null}

          {/* Map View */}
          <TouchableOpacity onPress={openMap} style={styles.mapContainer}>
            <MapView
              style={styles.map}
              initialRegion={{
                latitude: currentLocation?.coords.latitude || 5.6037,
                longitude: currentLocation?.coords.longitude || -0.1870,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              }}
              scrollEnabled={false}
              zoomEnabled={false}
            >
              {currentLocation && (
                <Marker
                  coordinate={{
                    latitude: currentLocation.coords.latitude,
                    longitude: currentLocation.coords.longitude,
                  }}
                />
              )}
            </MapView>
            <View style={styles.mapOverlay}>
              <TouchableOpacity style={styles.openButton}>
                <Ionicons name="location" size={20} color="#fff" />
                <Text style={styles.openButtonText}>Open</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>

          {/* Use Current Location Button */}
          <TouchableOpacity
            style={styles.currentLocationButton}
            onPress={getCurrentLocation}
            disabled={isLoadingLocation}
          >
            {isLoadingLocation ? (
              <ActivityIndicator size="small" color="#72BB53" />
            ) : (
              <>
                <Ionicons name="navigate" size={20} color="#72BB53" />
                <Text style={styles.currentLocationText}>Use Current Location</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
      <Modal
              isVisible={showMap}
              style={styles.mapModal}
              onBackdropPress={closeMap}
              onBackButtonPress={closeMap}
              animationIn="slideInUp"
              animationOut="slideOutDown"
            >
              <View style={styles.mapModalContainer}>
                <View style={styles.mapModalHeader}>
                  <Text style={styles.mapModalTitle}>Select Business Location</Text>
                  <TouchableOpacity onPress={closeMap}>
                    <Ionicons name="close" size={24} color="#333" />
                  </TouchableOpacity>
                </View>

                <MapView
                  ref={mapRef}
                  style={styles.fullScreenMap}
                  initialRegion={mapRegion}
                  onPress={selectLocationOnMap}
                >
                  {currentLocation && (
                    <Marker
                      coordinate={{
                        latitude: currentLocation.coords.latitude,
                        longitude: currentLocation.coords.longitude,
                      }}
                      draggable
                      onDragEnd={onMarkerDragEnd}
                    />
                  )}
                </MapView>

                <View style={styles.mapModalFooter}>
                  <Button
                    title="Confirm Location"
                    onPress={closeMap}
                    style={styles.confirmLocationButton}
                  />
                </View>
              </View>
            </Modal>

      <View style={styles.bottomContainer}>
        <Button
          title="Continue"
          onPress={handleSubmit}
          disabled={!isFormValid()}
        />
      </View>


      {/* Success Modal */}
      <SuccessModal
        visible={showSuccessModal}
        title="Profile Created!"
        message="Your supplier profile has been created successfully"
        onClose={handleSuccessModalClose}
      />


    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 20,
    paddingHorizontal: 5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 15,
  },
  input: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  dropdownButton: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputText: {
    fontSize: 16,
    color: '#000',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
  dropdownContainer: {
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    marginTop: 5,
    maxHeight: 200,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownScroll: {
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  locationContainer: {
    backgroundColor: '#F5F5F7',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
  },
  locationLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  locationText: {
    fontSize: 16,
    color: '#333',
  },
  mapContainer: {
    height: 150,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 15,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  openButton: {
    backgroundColor: '#72BB53',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  openButtonText: {
    color: '#fff',
    marginLeft: 5,
    fontWeight: '500',
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
  },
  currentLocationText: {
    marginLeft: 8,
    color: '#72BB53',
    fontWeight: '500',
  },
  mapModalContainer: {
    backgroundColor: '#fff',
    height: '90%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  mapModal: {
    margin: 0,
    justifyContent: 'flex-end',
  },

  mapModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EFEFEF',
  },
  mapModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  fullScreenMap: {
    flex: 1,
  },
  mapModalFooter: {
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#EFEFEF',
  },
  confirmLocationButton: {
    backgroundColor: '#72BB53',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmLocationButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  selectedProductsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 10,
  },
  selectedProductChip: {
    backgroundColor: '#72BB53',
    borderRadius: 16,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  selectedProductChipText: {
    color: '#fff',
    fontSize: 14,
  },
  selectedProductChipClose: {
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedDropdownItem: {
    backgroundColor: '#e0e0e0',
  },
  selectedDropdownItemText: {
    color: '#72BB53',
  },
  bottomContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#EFEFEF',
    backgroundColor: '#fff',
  },
});

export default SupplierProfileScreen;
