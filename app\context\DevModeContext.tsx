import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DevModeDialog from '../../components/DevModeDialog';

const DEV_MODE_KEY = 'dev_mode_shown';
const DEV_MODE_ENABLED = __DEV__; // Only show in development builds

interface DevModeContextType {
  isDevMode: boolean;
  showDevDialog: () => void;
  hideDevDialog: () => void;
}

const DevModeContext = createContext<DevModeContextType | undefined>(undefined);

export const DevModeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [showDialog, setShowDialog] = useState(false);
  const [isDevMode] = useState(DEV_MODE_ENABLED);

  useEffect(() => {
    const checkDevMode = async () => {
      if (!DEV_MODE_ENABLED) return;

      try {
        // Check if we've already shown the dev dialog in this session
        const hasShown = await AsyncStorage.getItem(DEV_MODE_KEY);
        
        // Show dialog if we haven't shown it yet in this session
        if (!hasShown) {
          // Add a small delay to ensure the app is fully loaded
          setTimeout(() => {
            setShowDialog(true);
          }, 1000);
        }
      } catch (error) {
        console.error('Error checking dev mode:', error);
      }
    };

    checkDevMode();
  }, []);

  const showDevDialog = () => {
    setShowDialog(true);
  };

  const hideDevDialog = async () => {
    setShowDialog(false);
    
    // Mark that we've shown the dialog in this session
    try {
      await AsyncStorage.setItem(DEV_MODE_KEY, 'true');
    } catch (error) {
      console.error('Error storing dev mode flag:', error);
    }
  };

  const contextValue = {
    isDevMode,
    showDevDialog,
    hideDevDialog,
  };

  return (
    <DevModeContext.Provider value={contextValue}>
      {children}
      {isDevMode && (
        <DevModeDialog
          visible={showDialog}
          onClose={hideDevDialog}
        />
      )}
    </DevModeContext.Provider>
  );
};

export const useDevMode = () => {
  const context = useContext(DevModeContext);
  if (!context) {
    throw new Error('useDevMode must be used within a DevModeProvider');
  }
  return context;
};

// This is a dummy component to satisfy Expo Router's requirement for a default export
const DevModeContextComponent = () => null;
export default DevModeContextComponent;
