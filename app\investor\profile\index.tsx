import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  ScrollView,
  StatusBar,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Button from '../../../components/Button';
import SuccessModal from '../../../components/SuccessModal';
import Slider from '@react-native-community/slider';
import * as Location from 'expo-location';
import MapView, { Marker, Region } from 'react-native-maps';
import Modal from 'react-native-modal';

// Investment interest categories
const INVESTMENT_CATEGORIES = [
  'Cash Crops',
  'Tubers',
  'Cereals',
  'Vegetables',
  'Fruits',
  'Livestock',
  'Poultry',
  'Aquaculture',
  'Other'
];

const InvestorProfileScreen: React.FC = () => {
  const mapRef = useRef<MapView>(null);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    investmentInterest: '',
    location: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [currency, setCurrency] = useState('GHC');
  const [priceRange, setPriceRange] = useState(500);
  const [investmentFrequency, setInvestmentFrequency] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Dropdown state
  const [showInterestDropdown, setShowInterestDropdown] = useState(false);

  // Form validation states
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [emailTouched, setEmailTouched] = useState(false);
  const [confirmPasswordTouched, setConfirmPasswordTouched] = useState(false);

  // Location related states
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [locationName, setLocationName] = useState('');
  const [showMap, setShowMap] = useState(false);
  const [mapRegion, setMapRegion] = useState<Region>({
    latitude: 5.6037,
    longitude: -0.1870,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  useEffect(() => {
    (async () => {
      let { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Permission to access location was denied');
        return;
      }
    })();
  }, []);

  // Validate email format
  useEffect(() => {
    if (emailTouched) {
      if (!formData.email) {
        setEmailError('Email is required');
      } else if (!formData.email.includes('@')) {
        setEmailError('Please include an @ in the email address');
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        setEmailError('Please enter a valid email address');
      } else {
        setEmailError('');
      }
    }
  }, [formData.email, emailTouched]);

  // Validate password match
  useEffect(() => {
    if (confirmPasswordTouched) {
      if (formData.password !== formData.confirmPassword) {
        setPasswordError('Passwords do not match');
      } else {
        setPasswordError('');
      }
    }
  }, [formData.password, formData.confirmPassword, confirmPasswordTouched]);

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Set touched state for validation fields
    if (field === 'email' && !emailTouched) {
      setEmailTouched(true);
    }
    if (field === 'confirmPassword' && !confirmPasswordTouched) {
      setConfirmPasswordTouched(true);
    }
  };

  const handleSubmit = () => {
    // Validate form data
    const requiredFields = ['fullName', 'email', 'password', 'confirmPassword', 'investmentInterest', 'location'] as const;
    const emptyFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);

    if (emptyFields.length > 0) {
      Alert.alert(
        "Missing Information",
        `Please fill in all required fields: ${emptyFields.join(', ')}`,
        [{ text: "OK" }]
      );
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert(
        "Password Mismatch",
        "Password and confirm password do not match",
        [{ text: "OK" }]
      );
      return;
    }

    if (emailError) {
      Alert.alert(
        "Invalid Email",
        "Please enter a valid email address",
        [{ text: "OK" }]
      );
      return;
    }

    // Submit form data
    console.log('Submitting investor profile:', {
      ...formData,
      currency,
      priceRange,
      investmentFrequency
    });
    setShowSuccessModal(true);
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    router.push({
      pathname: '/shared/settings',
      params: { fullName: formData.fullName }
    });
  };

  // Location related functions
  const openMap = () => {
    setShowMap(true);
  };

  const closeMap = () => {
    setShowMap(false);
  };

  const getCurrentLocation = async () => {
    setIsLoadingLocation(true);
    try {
      const location = await Location.getCurrentPositionAsync({});
      setCurrentLocation(location);

      // Reverse geocode to get address
      const geocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      });

      if (geocode.length > 0) {
        const address = geocode[0];
        const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
        setLocationName(locationString);
        handleChange('location', locationString);
      }

      // Update map region
      setMapRegion({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    } catch (error) {
      console.error('Error getting current location:', error);
      Alert.alert('Error', 'Failed to get current location');
    } finally {
      setIsLoadingLocation(false);
    }
  };

  const selectLocationOnMap = async (e: any) => {
    const { latitude, longitude } = e.nativeEvent.coordinate;

    // Update current location
    setCurrentLocation({
      coords: {
        latitude,
        longitude,
        altitude: null,
        accuracy: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    });

    // Reverse geocode to get address
    try {
      const geocode = await Location.reverseGeocodeAsync({
        latitude,
        longitude
      });

      if (geocode.length > 0) {
        const address = geocode[0];
        const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
        setLocationName(locationString);
        handleChange('location', locationString);
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }
  };

  const onMarkerDragEnd = async (e: any) => {
    const { latitude, longitude } = e.nativeEvent.coordinate;

    // Update current location
    setCurrentLocation({
      coords: {
        latitude,
        longitude,
        altitude: null,
        accuracy: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    });

    // Reverse geocode to get address
    try {
      const geocode = await Location.reverseGeocodeAsync({
        latitude,
        longitude
      });

      if (geocode.length > 0) {
        const address = geocode[0];
        const locationString = `${address.name || ''}, ${address.street || ''}, ${address.city || ''}, ${address.region || ''}, ${address.country || ''}`;
        setLocationName(locationString);
        handleChange('location', locationString);
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }
  };

  const priceRangeLabels = [100, 500, 1000, 1500, 3000, 5000];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.headerTitle}>Investor Registration</Text>

        <View style={styles.formGroup}>
          <TextInput
            style={styles.input}
            placeholder="Full Name"
            value={formData.fullName}
            onChangeText={(text) => handleChange('fullName', text)}
          />
        </View>

        <View style={styles.formGroup}>
          <TextInput
            style={[styles.input, emailError ? styles.inputError : null]}
            placeholder="Email"
            keyboardType="email-address"
            value={formData.email}
            onChangeText={(text) => handleChange('email', text)}
            onBlur={() => setEmailTouched(true)}
          />
          {emailError ? <Text style={styles.errorText}>{emailError}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <View style={styles.passwordContainer}>
            <TextInput
              style={styles.passwordInput}
              placeholder="Password"
              secureTextEntry={!showPassword}
              value={formData.password}
              onChangeText={(text) => handleChange('password', text)}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Ionicons name={showPassword ? "eye-off" : "eye"} size={24} color="#999" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.formGroup}>
          <View style={[styles.passwordContainer, passwordError ? styles.inputError : null]}>
            <TextInput
              style={styles.passwordInput}
              placeholder="Confirm Password"
              secureTextEntry={!showConfirmPassword}
              value={formData.confirmPassword}
              onChangeText={(text) => handleChange('confirmPassword', text)}
              onBlur={() => setConfirmPasswordTouched(true)}
            />
            <TouchableOpacity
              style={styles.eyeIcon}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <Ionicons name={showConfirmPassword ? "eye-off" : "eye"} size={24} color="#999" />
            </TouchableOpacity>
          </View>
          {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}
          {confirmPasswordTouched && !passwordError && formData.confirmPassword ?
            <Text style={styles.successText}>Passwords match</Text> : null}
        </View>

        {/* Investment Interest Dropdown */}
        <View style={styles.formGroup}>
          <TouchableOpacity
            style={styles.dropdownInput}
            onPress={() => setShowInterestDropdown(!showInterestDropdown)}
          >
            <Text style={formData.investmentInterest ? styles.inputText : styles.placeholderText}>
              {formData.investmentInterest || "Investment interest area"}
            </Text>
            <Ionicons name="chevron-down" size={20} color="#999" />
          </TouchableOpacity>

          {showInterestDropdown && (
            <View style={styles.dropdownContainer}>
              <ScrollView style={styles.dropdownScroll} nestedScrollEnabled={true}>
                {INVESTMENT_CATEGORIES.map((category, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.dropdownItem}
                    onPress={() => {
                      handleChange('investmentInterest', category);
                      setShowInterestDropdown(false);
                    }}
                  >
                    <Text style={styles.dropdownItemText}>{category}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>

        {/* Location Section */}
        <View style={styles.formGroup}>
          <Text style={styles.sectionTitle}>Business Location</Text>

          {locationName ? (
            <View style={styles.locationContainer}>
              <Text style={styles.locationLabel}>Current Set Location</Text>
              <Text style={styles.locationText}>{locationName}</Text>
            </View>
          ) : null}

          {/* Location Search Input */}
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search for a location..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={searchLocation}
            />
            <TouchableOpacity style={styles.searchButton} onPress={searchLocation}>
              <Ionicons name="search" size={20} color="#72BB53" />
            </TouchableOpacity>
          </View>

          {locationName ? (
            <View style={styles.locationContainer}>
              <Text style={styles.locationLabel}>Current Set Location</Text>
              <Text style={styles.locationText}>{locationName}</Text>
              <TouchableOpacity
                style={styles.changeLocationButton}
                onPress={() => setLocationName('')}
              >
                <Text style={styles.changeLocationText}>Change Location</Text>
              </TouchableOpacity>
            </View>
          ) : null}

          {/* Map View */}
          <View style={styles.mapContainer}>
            <MapView
              style={styles.map}
              initialRegion={{
                latitude: currentLocation?.coords.latitude || 5.6037,
                longitude: currentLocation?.coords.longitude || -0.1870,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              }}
              scrollEnabled={true}
              zoomEnabled={true}
              onPress={selectLocationOnMap}
            >
              {currentLocation && (
                <Marker
                  coordinate={{
                    latitude: currentLocation.coords.latitude,
                    longitude: currentLocation.coords.longitude,
                  }}
                  draggable
                  onDragEnd={onMarkerDragEnd}
                />
              )}
            </MapView>
            <View style={styles.mapOverlay}>
              <TouchableOpacity style={styles.openButton} onPress={openMap}>
                <Ionicons name="location" size={20} color="#fff" />
                <Text style={styles.openButtonText}>Open Full Map</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Use Current Location Button */}
          <TouchableOpacity
            style={styles.currentLocationButton}
            onPress={getCurrentLocation}
            disabled={isLoadingLocation}
          >
            {isLoadingLocation ? (
              <ActivityIndicator size="small" color="#72BB53" />
            ) : (
              <>
                <Ionicons name="navigate" size={20} color="#72BB53" />
                <Text style={styles.currentLocationText}>Use Current Location</Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Currency</Text>
          <View style={styles.radioContainer}>
            <TouchableOpacity
              style={styles.radioOption}
              onPress={() => setCurrency('GHC')}
            >
              <View style={styles.radioCircle}>
                {currency === 'GHC' && <View style={styles.radioSelected} />}
              </View>
              <Text style={styles.radioText}>GHC</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.radioOption}
              onPress={() => setCurrency('USD')}
            >
              <View style={styles.radioCircle}>
                {currency === 'USD' && <View style={styles.radioSelected} />}
              </View>
              <Text style={styles.radioText}>USD</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Funding Range</Text>
          <Slider
            style={styles.slider}
            minimumValue={0}
            maximumValue={5}
            step={1}
            value={priceRangeLabels.indexOf(priceRange)}
            onValueChange={(value) => setPriceRange(priceRangeLabels[value])}
            minimumTrackTintColor="#7AC142"
            maximumTrackTintColor="#D9D9D9"
            thumbTintColor="#7AC142"
          />
          <View style={styles.sliderLabels}>
            {priceRangeLabels.map((label, index) => (
              <Text key={index} style={styles.sliderLabel}>{label}</Text>
            ))}
          </View>
        </View>

        <View style={styles.frequencyContainer}>
          <Text style={styles.sectionTitle}>Investment Frequency</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {['Any', 'Monthly', 'Annually'].map((option) => (
              <TouchableOpacity
                key={option}
                style={[
                  styles.frequencyOption,
                  investmentFrequency === option && styles.frequencyOptionSelected
                ]}
                onPress={() => setInvestmentFrequency(option)}
              >
                <Text style={[
                  styles.frequencyText,
                  investmentFrequency === option && styles.frequencyTextSelected
                ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <Button
          title="Continue"
          onPress={handleSubmit}
          style={styles.submitButton}
        />
      </ScrollView>

      {/* Map Modal */}
      <Modal
        isVisible={showMap}
        style={styles.mapModal}
        onBackdropPress={closeMap}
        onBackButtonPress={closeMap}
        animationIn="slideInUp"
        animationOut="slideOutDown"
      >
        <View style={styles.mapModalContainer}>
          <View style={styles.mapModalHeader}>
            <Text style={styles.mapModalTitle}>Select Business Location</Text>
            <TouchableOpacity onPress={closeMap}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>

          <MapView
            ref={mapRef}
            style={styles.fullScreenMap}
            initialRegion={mapRegion}
            onPress={selectLocationOnMap}
          >
            {currentLocation && (
              <Marker
                coordinate={{
                  latitude: currentLocation.coords.latitude,
                  longitude: currentLocation.coords.longitude,
                }}
                draggable
                onDragEnd={onMarkerDragEnd}
              />
            )}
          </MapView>

          <View style={styles.mapModalFooter}>
            <Button
              title="Confirm Location"
              onPress={closeMap}
              style={styles.confirmLocationButton}
            />
          </View>
        </View>
      </Modal>

      <SuccessModal
        visible={showSuccessModal}
        title="Registration Successful!"
        message="Your investor profile has been created successfully."
        onClose={handleSuccessModalClose}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 10,
    paddingBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#000',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 16,
  },
  input: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: '#333',
  },
  inputError: {
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    marginTop: 4,
    marginLeft: 4,
  },
  successText: {
    color: '#34C759',
    fontSize: 14,
    marginTop: 4,
    marginLeft: 4,
  },
  passwordContainer: {
    flexDirection: 'row',
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
    padding: 16,
    fontSize: 16,
    color: '#333',
  },
  eyeIcon: {
    padding: 10,
  },
  dropdownInput: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inputText: {
    color: '#333',
    fontSize: 16,
  },
  placeholderText: {
    color: '#999',
    fontSize: 16,
  },
  // Location styles
  locationContainer: {
    backgroundColor: '#F5F5F7',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
  },
  locationLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 16,
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
  },
  searchInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  searchButton: {
    padding: 12,
    borderLeftWidth: 1,
    borderLeftColor: '#ddd',
  },
  changeLocationButton: {
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  changeLocationText: {
    color: '#72BB53',
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  mapContainer: {
    height: 200,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 10,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 10,
    right: 10,
  },
  openButton: {
    backgroundColor: '#72BB53',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 4,
  },
  openButtonText: {
    color: '#fff',
    marginLeft: 4,
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#72BB53',
    borderRadius: 8,
    padding: 12,
  },
  currentLocationText: {
    color: '#72BB53',
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '500',
  },
  mapModal: {
    margin: 0,
    justifyContent: 'flex-end',
  },
  mapModalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    padding: 20,
    height: '80%',
  },
  mapModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  mapModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  fullScreenMap: {
    height: '70%',
    borderRadius: 10,
    marginBottom: 20,
  },
  mapModalFooter: {
    alignItems: 'center',
  },
  confirmLocationButton: {
    width: '80%',
    backgroundColor: '#72BB53',
  },
  sectionContainer: {
    marginTop: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#333',
  },
  radioContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  radioCircle: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#999',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  radioSelected: {
    height: 12,
    width: 12,
    borderRadius: 6,
    backgroundColor: '#7AC142',
  },
  radioText: {
    fontSize: 16,
    color: '#333',
  },
  slider: {
    height: 40,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
  },
  sliderLabel: {
    fontSize: 14,
    color: '#666',
  },
  frequencyContainer: {
    marginVertical: 24,
  },
  frequencyOption: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
    backgroundColor: '#F5F5F7',
    marginRight: 10,
  },
  frequencyOptionSelected: {
    backgroundColor: '#7AC142',
  },
  frequencyText: {
    fontSize: 16,
    color: '#666',
  },
  frequencyTextSelected: {
    color: '#fff',
  },
  submitButton: {
    backgroundColor: '#7AC142',
    marginTop: 20,
    marginBottom: 40,
  },
  dropdownContainer: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    zIndex: 1000,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  dropdownScroll: {
    maxHeight: 150,
  },
  dropdownItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#333',
  },
});

export default InvestorProfileScreen;




