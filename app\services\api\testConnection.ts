import apiClient from './apiClient';

/**
 * Test the connection to the backend API
 * This function makes a simple GET request to the API to check if it's accessible
 */
export const testApiConnection = async (): Promise<boolean> => {
  try {
    // Try to fetch users as a simple test
    const response = await fetch('https://agricsupport.fly.dev/api/v1/user/get-users');
    
    // Log the response status
    console.log('API connection test status:', response.status);
    
    // If we get a response (even if it's unauthorized), the API is accessible
    return response.status !== 0;
  } catch (error) {
    console.error('API connection test error:', error);
    return false;
  }
};

/**
 * Test the login endpoint
 * This function attempts to log in with test credentials
 */
export const testLogin = async (): Promise<{success: boolean, message: string}> => {
  try {
    // Test credentials - these should be replaced with real test credentials
    const testCredentials = {
      email: '<EMAIL>',
      password: 'password123'
    };
    
    const response = await apiClient.post('/api/v1/user/login', testCredentials, false);
    
    console.log('Login test response:', response);
    
    if (response.data) {
      return { 
        success: true, 
        message: 'Login successful' 
      };
    } else {
      return { 
        success: false, 
        message: response.error || 'Login failed with status ' + response.status 
      };
    }
  } catch (error) {
    console.error('Login test error:', error);
    return { 
      success: false, 
      message: 'Login test error: ' + (error instanceof Error ? error.message : String(error)) 
    };
  }
};
